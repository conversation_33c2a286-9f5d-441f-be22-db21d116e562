#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   03.IR_Pair_Find_Parallel-2.py
@Time    :   2025/07/16 11:28:29
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import numpy as np
import re
import string

def process_bed_coordinates(chr, exon1_start, exon2_stop, strand, exon1_len, exon2_len):
    """
    处理BED文件坐标，完全按照AltAnalyze逻辑
    
    参数:
        chr: 染色体名称
        exon1_start: 第一个exon起始位置
        exon2_stop: 第二个exon结束位置  
        strand: 链方向 (+/-)
        exon1_len: 第一个exon长度
        exon2_len: 第二个exon长度
    
    返回:
        tuple: (chr, exon1_stop, exon2_start) - AltAnalyze使用的坐标键
    """
    
    # 染色体名称标准化 - 完全按照AltAnalyze逻辑
    if 'chr' not in chr:
        chr = 'chr' + chr
    if chr == 'chrM': 
        chr = 'chrMT'  # MT是Ensembl约定
    if chr == 'M': 
        chr = 'MT'
    
    # 坐标计算 - 完全按照AltAnalyze逻辑
    if strand == '-':  # 负链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
        
        # 负链关键步骤：交换exon顺序
        a = (exon1_start, exon1_stop)
        b = (exon2_start, exon2_stop)
        exon1_stop, exon1_start = b  # 将b的值赋给exon1
        exon2_stop, exon2_start = a  # 将a的值赋给exon2
        
    else:  # 正链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
    return (chr, exon1_stop, exon2_start)


def bed_file_Process(sample_data):
    for i in sample_data.index:
        chr = sample_data.loc[i,0]
        exon1_start = sample_data.loc[i,1]
        exon2_stop = sample_data.loc[i,2]
        strand = sample_data.loc[i,5]
        #IntronInfo = sample_data.loc[i,"IntronInfo"]
        #SpliceSite = sample_data.loc[i,"SpliceSite"]
        lengths = sample_data.loc[i,10]
        lengths_info = re.split(',', lengths)
        exon1_len = lengths_info[0]
        exon2_len = lengths_info[1]
        exon1_len = int(exon1_len)
        exon2_len = int(exon2_len)
        exon1_start = int(exon1_start)
        exon2_stop = int(exon2_stop)
        chr, exon1_stop, exon2_start = process_bed_coordinates(chr, exon1_start, exon2_stop, strand, exon1_len, exon2_len)
        pos= f"{chr}:{exon1_stop}-{exon2_start}"
        sample_data.loc[i,"pos"] = pos
    ##########################################################################################
    ##########################################################################################
    sample_data = sample_data[[0,3,4,5,"pos"]]
    sample_data.columns = ["chr","junction_id","support_reads","strand","pos_AltAnalyze"]
    sample_data = sample_data.copy()
    sample_data[["IntronInfo","SpliceSite"]] = sample_data["junction_id"].str.split('-',expand=True)
    sample_data["SpliceSite"] = sample_data["SpliceSite"].astype(int)
    #print(sample_data)
    odd_sample = sample_data[sample_data.index % 2 == 0]
    even_sample = sample_data[sample_data.index % 2 == 1]
    odd_sample.columns = ["chr_x","junction_id_x","support_reads_x","strand_x","pos_AltAnalyze_x","IntronInfo_x","SpliceSite_x"]
    even_sample.columns = ["chr_y","junction_id_y","support_reads_y","strand_y","pos_AltAnalyze_y","IntronInfo_y","SpliceSite_y"]
    combined_data = pd.concat([odd_sample.reset_index(drop=True), even_sample.reset_index(drop=True)], axis=1)
    combined_data["pos_IGV"] = combined_data["chr_x"] + ":" + (combined_data[["SpliceSite_x","SpliceSite_y"]].min(axis=1)).astype(str) + "-" + (combined_data[["SpliceSite_x","SpliceSite_y"]].max(axis=1)).astype(str)
    combined_data["SpliceInfo"] = combined_data["IntronInfo_x"] + "_" + combined_data["pos_IGV"]
    #print(odd_sample)
    #print(even_sample)
    #print(combined_data)
    combined_data_1 = combined_data[["pos_AltAnalyze_x","SpliceInfo","pos_IGV","strand_x"]]
    combined_data_2 = combined_data[["pos_AltAnalyze_y","SpliceInfo","pos_IGV","strand_y"]]
    combined_data_1.columns = ["pos_Examined","SpliceInfo","pos_IGV","Strand"]
    combined_data_2.columns = ["pos_Examined","SpliceInfo","pos_IGV","Strand"]
    combined_data = pd.concat([combined_data_1, combined_data_2], axis=0, ignore_index= True)
    return combined_data

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/02.Sample_Level_AltSplice.txt"
bed_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze.bak2/bed"
out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/03.SampleSplice_IR_Anno_new.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = ID_Data[ID_Data["EventAnnotation"]=="intron-retention"]
ID_Data = ID_Data.sort_values(by="SampleID", ascending=False)
ID_Data = ID_Data.sort_values(by=["SampleID","Alt_Splice","SupportReads"],ascending=[True,True,False])
#ID_Data = ID_Data.head(50)
sample_list = list(np.unique(ID_Data["SampleID"]))

OutPut = []
runSample = 1
for sample in sample_list:
    print(runSample)
    ID_Data_sub = ID_Data[ID_Data["SampleID"]==sample]
    sample_id = re.split("\.",sample)[0]
    sample_file = f"{bed_dir}/{sample_id}.Aligned.sortedByCoord.out__intronJunction.bed"
    sample_data = pd.read_csv(sample_file,sep="\t",header=None)
    #print("process sample_data")
    combined_data = bed_file_Process(sample_data)
    ID_Data_sub = pd.merge(ID_Data_sub,combined_data,how='left',on='pos_Examined')
    #ID_Data_sub = pd.merge(ID_Data_sub,combined_data_2,how='left',on='pos_Examined')
    OutPut.append(ID_Data_sub)
    runSample = runSample + 1
    
OutPut = pd.concat(OutPut, axis=0, ignore_index= True)
OutPut.to_csv(out_File,sep="\t",header=True,index=False)
