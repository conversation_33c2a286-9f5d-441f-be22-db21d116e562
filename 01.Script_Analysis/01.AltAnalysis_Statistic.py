#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   AltAnalysis.py
@Time    :   2025/06/10 15:04:32
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import numpy as np
import anndata as ad
import os
from scipy.sparse import csr_matrix, csc_matrix  # 用于判断稀疏矩阵类型
from scipy import stats
from statsmodels.stats.multitest import multipletests

def h5ad_to_dataframe(adata):
    #"""将anndata对象的X矩阵转换为DataFrame（包含行/列索引）"""
    # 1. 处理X矩阵（稀疏矩阵转密集数组）
    if isinstance(adata.X, (csr_matrix, csc_matrix)):
        X_array = adata.X.toarray()  # 稀疏矩阵转numpy数组
    else:
        X_array = adata.X  # 直接使用原数组（如已是numpy数组）
    # 2. 转换为DataFrame（行索引：样本名，列索引：特征名）
    df = pd.DataFrame(
        data=X_array,
        index=adata.obs.index,       # 行索引：样本ID（如TCGA样本名）
        columns=adata.var.index      # 列索引：特征名（如junction名称）
    )
    return df

#The numerical value represents the number of reads that support the occurence of a certain junction
purned_counts_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze.bak2/altanalyze_output/ExpressionInput/counts.original.pruned.txt"
Cancer_Info = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/03.Sample_Info/00.Liver_Cancer_Sample_Info.txt"
db_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/02.database/SNAF_download/data"
outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/01.AltSplice_Statistic.txt"
######################
####统计癌症样本的junction表达情况
purned_counts_data = pd.read_csv(purned_counts_file,sep="\t",header=0,skiprows=0)
purned_counts_data = purned_counts_data.rename(columns={'Unnamed: 0': 'Alt_Splice'})
columns_to_keep = purned_counts_data.columns[purned_counts_data.columns != 'Alt_Splice']
purned_counts_data["Cancer_Mean"] = purned_counts_data[columns_to_keep].mean(axis=1)
purned_counts_data["Cancer_Median"] = purned_counts_data[columns_to_keep].median(axis=1)
purned_counts_data["Cancer_Min"] = purned_counts_data[columns_to_keep].min(axis=1)
purned_counts_data["Cancer_Max"] = purned_counts_data[columns_to_keep].max(axis=1)
purned_counts_data['Cancer_SampleNumber'] = (purned_counts_data[columns_to_keep] > 0).sum(axis=1)
purned_counts_data['Cancer_SampleRatio'] = (purned_counts_data[columns_to_keep] > 0).sum(axis=1)/len(columns_to_keep)
purned_counts_data_TJ = purned_counts_data[["Alt_Splice","Cancer_Mean","Cancer_Median","Cancer_Min","Cancer_Max","Cancer_SampleNumber","Cancer_SampleRatio"]]
##
######################
####癌症样本临床信息
#liver_Cancer_Info = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/00.Liver_Cancer_Sample_Info.txt"
Cancer_Data = pd.read_csv(Cancer_Info,sep="\t",header=0,skiprows=0)
Cancer_Data["sample_bed_id"] = Cancer_Data["SampleID"] + ".Aligned.sortedByCoord.out.bed"
##
##
######################
####正常样本junction表达情况
gtex_skin_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','gtex_skin_count.h5ad'))
gtex_skin_ctrl_df = h5ad_to_dataframe(gtex_skin_ctrl_db)
gtex_skin_ctrl_df = gtex_skin_ctrl_df.loc[gtex_skin_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
gtex_skin_columns_names = gtex_skin_ctrl_df.keys()
gtex_skin_ctrl_df["Mean"] = gtex_skin_ctrl_df[gtex_skin_columns_names].mean(axis=1)
gtex_skin_ctrl_df["Median"] = gtex_skin_ctrl_df[gtex_skin_columns_names].median(axis=1)
gtex_skin_ctrl_df["Min"] = gtex_skin_ctrl_df[gtex_skin_columns_names].min(axis=1)
gtex_skin_ctrl_df["Max"] = gtex_skin_ctrl_df[gtex_skin_columns_names].max(axis=1)
gtex_skin_ctrl_df['Number'] = (gtex_skin_ctrl_df[gtex_skin_columns_names] > 0).sum(axis=1)
gtex_skin_ctrl_df['Ratio'] = (gtex_skin_ctrl_df[gtex_skin_columns_names] > 0).sum(axis=1)/len(gtex_skin_columns_names)
##
##
gtex_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','GTEx_junction_counts.h5ad'))
gtex_ctrl_df = h5ad_to_dataframe(gtex_ctrl_db)
gtex_ctrl_df = gtex_ctrl_df.loc[gtex_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
gtex_columns_names = gtex_ctrl_df.keys()
gtex_ctrl_df["Mean"] = gtex_ctrl_df[gtex_columns_names].mean(axis=1)
gtex_ctrl_df["Median"] = gtex_ctrl_df[gtex_columns_names].median(axis=1)
gtex_ctrl_df["Min"] = gtex_ctrl_df[gtex_columns_names].min(axis=1)
gtex_ctrl_df["Max"] = gtex_ctrl_df[gtex_columns_names].max(axis=1)
gtex_ctrl_df['Number'] = (gtex_ctrl_df[gtex_columns_names] > 0).sum(axis=1)
gtex_ctrl_df['Ratio'] = (gtex_ctrl_df[gtex_columns_names] > 0).sum(axis=1)/len(gtex_columns_names)
##
tcga_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','tcga_matched_control_junction_count.h5ad'))
tcga_ctrl_df = h5ad_to_dataframe(tcga_ctrl_db)
tcga_ctrl_df = tcga_ctrl_df.loc[tcga_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
tcga_columns_names = tcga_ctrl_df.keys()
tcga_ctrl_df["Mean"] = tcga_ctrl_df[tcga_columns_names].mean(axis=1)
tcga_ctrl_df["Median"] = tcga_ctrl_df[tcga_columns_names].median(axis=1)
tcga_ctrl_df["Min"] = tcga_ctrl_df[tcga_columns_names].min(axis=1)
tcga_ctrl_df["Max"] = tcga_ctrl_df[tcga_columns_names].max(axis=1)
tcga_ctrl_df['Number'] = (tcga_ctrl_df[tcga_columns_names] > 0).sum(axis=1)
tcga_ctrl_df['Ratio'] = (tcga_ctrl_df[tcga_columns_names] > 0).sum(axis=1)/len(tcga_columns_names)
###
######################
######################
####junction表达情况合并处理
purned_counts_data_TJ = purned_counts_data_TJ.copy()
purned_counts_data_TJ["Cancer_alt_dataset_num"] = 0
purned_counts_data_TJ["Cancer_all_dataset_num"] = 0
purned_counts_data_TJ["Cancer_dataset_ratio"] = 0.0
purned_counts_data_TJ["Skin_Mean"] = 0.0
purned_counts_data_TJ["Skin_Median"] = 0
purned_counts_data_TJ["Skin_Min"] = 0
purned_counts_data_TJ["Skin_Max"] = 0
purned_counts_data_TJ["Skin_SampleRatio"] = 0.0
purned_counts_data_TJ["Skin_Pvalue"] = 0.0
purned_counts_data_TJ["Skin_adjPvalue"] = 0.0
purned_counts_data_TJ["Gtex_Mean"] = 0.0
purned_counts_data_TJ["Gtex_Median"] = 0
purned_counts_data_TJ["Gtex_Min"] = 0
purned_counts_data_TJ["Gtex_Max"] = 0
purned_counts_data_TJ["Gtex_SampleRatio"] = 0.0
purned_counts_data_TJ["Gtex_Pvalue"] = 0.0
purned_counts_data_TJ["Gtex_adjPvalue"] = 0.0
purned_counts_data_TJ["TCGA_Mean"] = 0.0
purned_counts_data_TJ["TCGA_Median"] = 0
purned_counts_data_TJ["TCGA_Min"] = 0
purned_counts_data_TJ["TCGA_Max"] = 0
purned_counts_data_TJ["TCGA_SampleRatio"] = 0.0
purned_counts_data_TJ["TCGA_Pvalue"] = 0.0
purned_counts_data_TJ["TCGA_adjPvalue"] = 0.0
purned_counts_data_TJ["All_Pvalue"] = 0.0
purned_counts_data_TJ["All_adjPvalue"] = 0.0

def diff_Test(test_data,test_col_name,ctrl_data,ctrl_col_name):
    Alt_Splice = test_data["Alt_Splice"].values[0]
    test_values = list(test_data[test_col_name].values[0])
    try:
        ctrl_data = ctrl_data.loc[[Alt_Splice]]
        ctrl_mean = ctrl_data["Mean"].values[0]
        ctrl_Median = ctrl_data["Median"].values[0]
        ctrl_Min = ctrl_data["Min"].values[0]
        ctrl_Max = ctrl_data["Max"].values[0]
        ctrl_Ratio = ctrl_data["Ratio"].values[0]
        ctrl_Number = ctrl_data["Number"].values[0]
        ctrl_values = list(ctrl_data[ctrl_col_name].values[0])
    except:
        ctrl_values = []
    if len(ctrl_values) == 0:
        p_value = 0
        ctrl_mean = 0
        ctrl_Median = 0
        ctrl_Min = 0
        ctrl_Max = 0
        ctrl_Ratio = 0
        ctrl_Number = 0
    else:
        t, p = stats.ttest_ind(
                test_values, 
                ctrl_values,
                equal_var=False  # 默认使用Welch's t-test（方差不齐）
            )
        p_value = p
    #print(test_values)
    #print(ctrl_values)
    #print(f"{Alt_Splice}\t{len(test_values)}\t{len(ctrl_values)}\t{p_value}")
    return p_value,ctrl_mean,ctrl_Median,ctrl_Min,ctrl_Max,ctrl_Ratio,ctrl_Number 

def diff_Test_2(test_data,test_col_name,ctrl_data,ctrl_col_name):
    Alt_Splice = test_data["Alt_Splice"].values[0]
    test_values = list(test_data[test_col_name].values[0])
    try:
        ctrl_data = ctrl_data.loc[[Alt_Splice]]
        ctrl_values = list(ctrl_data[ctrl_col_name].values[0])
    except:
        ctrl_values = []
    if len(ctrl_values) == 0:
        p_value = 0
    else:
        t, p = stats.ttest_ind(
                test_values, 
                ctrl_values,
                equal_var=False  # 默认使用Welch's t-test（方差不齐）
            )
        p_value = p
    #print(test_values)
    #print(ctrl_values)
    #print(f"{Alt_Splice}\t{len(test_values)}\t{len(ctrl_values)}\t{p_value}")
    return p_value  
  

#purned_counts_data_TJ = purned_counts_data_TJ.head(50)
for i in purned_counts_data_TJ.index:
    tumor_data = purned_counts_data.loc[[i]]
    Alt_Splice = tumor_data["Alt_Splice"].values[0]
    ######
    p_value_gtex_ctrl,ctrl_mean,ctrl_Median,ctrl_Min,ctrl_Max,ctrl_Ratio,ctrl_Number = diff_Test(tumor_data,columns_to_keep,gtex_ctrl_df,gtex_columns_names)
    purned_counts_data_TJ.loc[i,"Gtex_Pvalue"] = p_value_gtex_ctrl
    purned_counts_data_TJ.loc[i,"Gtex_Mean"] = ctrl_mean
    purned_counts_data_TJ.loc[i,"Gtex_Median"] = ctrl_Median
    purned_counts_data_TJ.loc[i,"Gtex_Min"] = ctrl_Min
    purned_counts_data_TJ.loc[i,"Gtex_Max"] = ctrl_Max
    purned_counts_data_TJ.loc[i,"Gtex_SampleRatio"] = ctrl_Ratio
    purned_counts_data_TJ.loc[i,"Gtex_SampleNumber"] = ctrl_Number
    ######
    p_value_gtex_skin,ctrl_mean,ctrl_Median,ctrl_Min,ctrl_Max,ctrl_Ratio,ctrl_Number = diff_Test(tumor_data,columns_to_keep,gtex_skin_ctrl_df,gtex_skin_columns_names)
    purned_counts_data_TJ.loc[i,"Skin_Pvalue"] = p_value_gtex_skin
    purned_counts_data_TJ.loc[i,"Skin_Mean"] = ctrl_mean
    purned_counts_data_TJ.loc[i,"Skin_Median"] = ctrl_Median
    purned_counts_data_TJ.loc[i,"Skin_Min"] = ctrl_Min
    purned_counts_data_TJ.loc[i,"Skin_Max"] = ctrl_Max
    purned_counts_data_TJ.loc[i,"Skin_SampleRatio"] = ctrl_Ratio
    purned_counts_data_TJ.loc[i,"Skin_SampleNumber"] = ctrl_Number
    ######
    p_value_tcga,ctrl_mean,ctrl_Median,ctrl_Min,ctrl_Max,ctrl_Ratio,ctrl_Number = diff_Test(tumor_data,columns_to_keep,tcga_ctrl_df,tcga_columns_names)
    purned_counts_data_TJ.loc[i,"TCGA_Pvalue"] = p_value_tcga
    purned_counts_data_TJ.loc[i,"TCGA_Mean"] = ctrl_mean
    purned_counts_data_TJ.loc[i,"TCGA_Median"] = ctrl_Median
    purned_counts_data_TJ.loc[i,"TCGA_Min"] = ctrl_Min
    purned_counts_data_TJ.loc[i,"TCGA_Max"] = ctrl_Max
    purned_counts_data_TJ.loc[i,"TCGA_SampleRatio"] = ctrl_Ratio
    purned_counts_data_TJ.loc[i,"TCGA_SampleNumber"] = ctrl_Number
    #########################################################
    #########################################################
    All_Sub = []
    All_Col_Names = []
    try:
        gtex_ctrl_df_sub = gtex_ctrl_df.loc[[Alt_Splice]]
    except:
        gtex_ctrl_df_sub = []
    try:
        gtex_skin_ctrl_df_sub = gtex_skin_ctrl_df.loc[[Alt_Splice]]
    except:
        gtex_skin_ctrl_df_sub = []
    try:
        tcga_ctrl_df_sub = tcga_ctrl_df.loc[[Alt_Splice]]
    except:
        tcga_ctrl_df_sub = []
    if len(gtex_ctrl_df_sub) > 0:
        All_Col_Names = All_Col_Names + list(gtex_columns_names)
        if len(All_Sub) > 0:
           All_Sub = pd.concat([All_Sub, gtex_ctrl_df_sub], axis=1)
        else:
           All_Sub = gtex_ctrl_df_sub
    if len(gtex_skin_ctrl_df_sub) > 0:
        All_Col_Names = All_Col_Names + list(gtex_skin_columns_names)
        if len(All_Sub) > 0:
           All_Sub = pd.concat([All_Sub, gtex_skin_ctrl_df_sub], axis=1)
        else:
           All_Sub = gtex_skin_ctrl_df_sub
    if len(tcga_ctrl_df_sub) > 0:
        All_Col_Names = All_Col_Names + list(tcga_columns_names)
        if len(All_Sub) > 0:
           All_Sub = pd.concat([All_Sub, tcga_ctrl_df_sub], axis=1)
        else:
           All_Sub = tcga_ctrl_df_sub
    p_value_all = diff_Test_2(tumor_data,columns_to_keep,All_Sub,All_Col_Names)
    purned_counts_data_TJ.loc[i,"All_Pvalue"] = p_value_all
    #########################################################
    #########################################################
    #########################################################
    mask = tumor_data[columns_to_keep] > 0
    colinfo = mask.apply(lambda row: row.index[row].tolist(), axis=1)
    colinfo_List = colinfo.values[0]
    all_dataset_info = Cancer_Data[Cancer_Data["sample_bed_id"].isin(columns_to_keep)]
    alt_dataset_info = Cancer_Data[Cancer_Data["sample_bed_id"].isin(colinfo_List)]
    all_dataset_num = len(np.unique(all_dataset_info["BioProject"].values))
    alt_dataset_num = len(np.unique(alt_dataset_info["BioProject"].values))
    dataset_ratio = round(alt_dataset_num/all_dataset_num,4)
    purned_counts_data_TJ.loc[i,"Cancer_alt_dataset_num"] = alt_dataset_num
    purned_counts_data_TJ.loc[i,"Cancer_all_dataset_num"] = all_dataset_num
    purned_counts_data_TJ.loc[i,"Cancer_dataset_ratio"] = dataset_ratio
    #########################################################
    #########################################################
    #print(f"{Alt_Splice}\t{p_value_gtex_skin}\t{p_value_gtex_ctrl}\t{p_value_tcga}\t{p_value_all}\t{alt_dataset_num}\t{all_dataset_num}\t{dataset_ratio}")

_,Gtex_Pvalue_List_Adj,_,_ = multipletests(purned_counts_data_TJ["Gtex_Pvalue"].values,alpha=0.05,method='fdr_bh')
_,Skin_Pvalue_List_Adj,_,_ = multipletests(purned_counts_data_TJ["Skin_Pvalue"].values,alpha=0.05,method='fdr_bh')
_,TCGA_Pvalue_List_Adj,_,_ = multipletests(purned_counts_data_TJ["TCGA_Pvalue"].values,alpha=0.05,method='fdr_bh')
_,All_Pvalue_List_Adj,_,_ = multipletests(purned_counts_data_TJ["All_Pvalue"].values,alpha=0.05,method='fdr_bh')

purned_counts_data_TJ["Skin_adjPvalue"] = Skin_Pvalue_List_Adj
purned_counts_data_TJ["Gtex_adjPvalue"] = Gtex_Pvalue_List_Adj
purned_counts_data_TJ["TCGA_adjPvalue"] = TCGA_Pvalue_List_Adj
purned_counts_data_TJ["All_adjPvalue"] = All_Pvalue_List_Adj

purned_counts_data_TJ.to_csv(outfile,header=True,index=False,sep="\t")
