#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   08.Exon_Other_bed-2.py
@Time    :   2025/07/15 15:24:18
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
import os
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result"
splice_type = ["cassette-exon","alt3","alt5","altC","altPromoter","altTransSplice"]
gene_first_exon = f"/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon_gene_first-exon.bed"

def filter_cassette_exon(leftpos_intersect_file,rightpos_intersect_file,inner_exon_intersect_file,inner_intron_intersect_file):
    leftpos_intersect_data = pd.read_csv(leftpos_intersect_file, sep="\t", header=None, skiprows=0)
    rightpos_intersect_data = pd.read_csv(rightpos_intersect_file, sep="\t", header=None, skiprows=0)
    inner_exon_intersect_data = pd.read_csv(inner_exon_intersect_file, sep="\t", header=None, skiprows=0)
    inner_intron_intersect_data = pd.read_csv(inner_intron_intersect_file, sep="\t", header=None, skiprows=0)
    ########################################
    ########################################
    outdataframe = leftpos_intersect_data[[3]]
    outdataframe = outdataframe.drop_duplicates()
    #print(leftpos_intersect_data)
    leftpos_intersect_data["gene_Examined"] = leftpos_intersect_data[3].str.split(":",expand=True)[0]
    leftpos_intersect_data[["gene_anno","trans_anno"]] = leftpos_intersect_data[8].str.split(":",expand=True)
    leftpos_intersect_data_samegene = leftpos_intersect_data[leftpos_intersect_data["gene_Examined"]==leftpos_intersect_data["gene_anno"]]
    leftpos_intersect_data_samegene = leftpos_intersect_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(leftpos_intersect_data_samegene)
    ########################################
    ########################################
    #print(leftpos_intersect_data)
    rightpos_intersect_data["gene_Examined"] = rightpos_intersect_data[3].str.split(":",expand=True)[0]
    rightpos_intersect_data[["gene_anno","trans_anno"]] = rightpos_intersect_data[8].str.split(":",expand=True)
    rightpos_intersect_data_samegene = rightpos_intersect_data[rightpos_intersect_data["gene_Examined"]==rightpos_intersect_data["gene_anno"]]
    rightpos_intersect_data_samegene = rightpos_intersect_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(rightpos_intersect_data_samegene)
    ########################################
    ########################################
    #print(inner_exon_intersect_data)
    inner_exon_intersect_data["len"] = inner_exon_intersect_data[7] - inner_exon_intersect_data[6]
    inner_exon_intersect_data["intersect_ratio"] = inner_exon_intersect_data[10] / inner_exon_intersect_data["len"]
    inner_exon_intersect_data["gene_Examined"] = inner_exon_intersect_data[3].str.split(":",expand=True)[0]
    inner_exon_intersect_data[["gene_anno","trans_anno"]] = inner_exon_intersect_data[8].str.split(":",expand=True)
    inner_exon_intersect_data_samegene = inner_exon_intersect_data[inner_exon_intersect_data["gene_Examined"]==inner_exon_intersect_data["gene_anno"]]
    inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene[inner_exon_intersect_data_samegene["intersect_ratio"]==1]
    inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(inner_exon_intersect_data_samegene)
    ########################################
    ########################################
    #print(inner_intron_intersect_data)
    inner_intron_intersect_data["len"] = inner_intron_intersect_data[7] - inner_intron_intersect_data[6]
    inner_intron_intersect_data["intersect_ratio"] = inner_intron_intersect_data[10] / inner_intron_intersect_data["len"]
    inner_intron_intersect_data["gene_Examined"] = inner_intron_intersect_data[3].str.split(":",expand=True)[0]
    inner_intron_intersect_data[["gene_anno","trans_anno"]] = inner_intron_intersect_data[8].str.split(":",expand=True)
    inner_intron_intersect_data_samegene = inner_intron_intersect_data[inner_intron_intersect_data["gene_Examined"]==inner_intron_intersect_data["gene_anno"]]
    inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene[inner_intron_intersect_data_samegene["intersect_ratio"]==1]
    inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene.groupby([3,"trans_anno"]).agg({"intersect_ratio":"sum"}).reset_index()
    inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene[inner_intron_intersect_data_samegene["intersect_ratio"]>=2]
    inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    inner_intron_intersect_data_samegene["intersect_ratio"] = 2
    #print(inner_intron_intersect_data_samegene)
    #print(inner_intron_intersect_data_diffgene)
    ########################################
    ########################################
    outdataframe = pd.merge(outdataframe,leftpos_intersect_data_samegene,how='left',on=[3])
    outdataframe = pd.merge(outdataframe,rightpos_intersect_data_samegene,how='left',on=[3])
    #print(outdataframe)
    outdataframe.columns = [3,"leftpos_samegene","leftpos_samegene_trans","rightpos_samegene","rightpos_samegene_trans"]
    outdataframe = pd.merge(outdataframe,inner_exon_intersect_data_samegene,how='left',on=[3])
    outdataframe = pd.merge(outdataframe,inner_intron_intersect_data_samegene,how='left',on=[3])
    outdataframe.columns = ["Alt_Splice","leftpos_samegene","leftpos_samegene_trans","rightpos_samegene","rightpos_samegene_trans","inner_exon_samegene","inner_exon_samegene_trans","inner_intron_samegene_trans","inner_intron_samegene"]
    outdataframe = outdataframe.fillna(0)
    #print(outdataframe)
    outdataframe["Report"] = "YES"
    outdataframe["TranscriptDetail"] = "YES"
    for i in outdataframe.index:
        leftpos_samegene_trans = re.split("\|",str(outdataframe.loc[i,"leftpos_samegene_trans"]))
        rightpos_samegene_trans = re.split("\|",str(outdataframe.loc[i,"rightpos_samegene_trans"]))
        inner_exon_samegene_trans = re.split("\|",str(outdataframe.loc[i,"inner_exon_samegene_trans"]))
        inner_intron_samegene_trans = re.split("\|",str(outdataframe.loc[i,"inner_intron_samegene_trans"]))
        common_elements = set(leftpos_samegene_trans) & set(rightpos_samegene_trans) & set(inner_exon_samegene_trans) & set(inner_intron_samegene_trans)
        common_elements = list(common_elements)
        if "0" in common_elements:
            common_elements.remove("0") 
        if len(common_elements) > 0:
            Trans = "YES"
            TransDetail = "|".join(common_elements)
        else:
            Trans = "NO"
            TransDetail = "-"
        outdataframe.loc[i,"Report"] = Trans
        outdataframe.loc[i,"TranscriptDetail"] = TransDetail
    #print(outdataframe)
    return outdataframe
########################################
########################################
def filter_alt3(alt_splice_unchange,alt_splice_change,alt_splice_inner_intron,alt_splice_inner_exon):
    alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
    alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
    alt_splice_inner_intron_data = pd.read_csv(alt_splice_inner_intron, sep="\t", header=None, skiprows=0)
    alt_splice_inner_exon_data = pd.read_csv(alt_splice_inner_exon, sep="\t", header=None, skiprows=0)
    ########################################
    ########################################
    #print(alt_splice_unchange_data)
    alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
    alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[8].str.split(":",expand=True)
    alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
    alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    ########################################
    ########################################
    #print(alt_splice_change_data)
    alt_splice_change_data = alt_splice_change_data[[3,10]]
    alt_splice_change_data = alt_splice_change_data.drop_duplicates()
    #alt_splice_change_data.columns = ["Alt_Splice","ChangeSite"]
    #print(alt_splice_change_data)
    ########################################
    ########################################
    #print(alt_splice_inner_intron_data)
    alt_splice_inner_intron_data["len"] = alt_splice_inner_intron_data[7] - alt_splice_inner_intron_data[6]
    alt_splice_inner_intron_data["intersect_ratio"] = alt_splice_inner_intron_data[10] / alt_splice_inner_intron_data["len"]
    alt_splice_inner_intron_data["gene_Examined"] = alt_splice_inner_intron_data[3].str.split(":",expand=True)[0]
    alt_splice_inner_intron_data[["gene_anno","trans_anno"]] = alt_splice_inner_intron_data[8].str.split(":",expand=True)
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data[alt_splice_inner_intron_data["gene_Examined"]==alt_splice_inner_intron_data["gene_anno"]]
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene[alt_splice_inner_intron_data_samegene["intersect_ratio"]==1]
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    ########################################
    ########################################
    #print(alt_splice_inner_exon_data)
    alt_splice_inner_exon_data["len"] = alt_splice_inner_exon_data[7] - alt_splice_inner_exon_data[6]
    alt_splice_inner_exon_data["intersect_ratio"] = alt_splice_inner_exon_data[10] / alt_splice_inner_exon_data["len"]
    alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data["intersect_ratio"]<1]
    alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data[10]>=2]
    alt_splice_inner_exon_data["gene_Examined"] = alt_splice_inner_exon_data[3].str.split(":",expand=True)[0]
    alt_splice_inner_exon_data[["gene_anno","trans_anno"]] = alt_splice_inner_exon_data[8].str.split(":",expand=True)
    alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data[alt_splice_inner_exon_data["gene_Examined"]==alt_splice_inner_exon_data["gene_anno"]]
    alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data_samegene.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    alt_splice_inner_exon_data_samegene["ExonIntersect"] = 2
    ########################################
    ########################################
    alt_splice_change_data.columns = ["Alt_Splice","ChangeInfo"]
    alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
    alt_splice_inner_intron_data_samegene.columns = ["Alt_Splice","IntronIntersect","IntronTrans"]
    alt_splice_inner_exon_data_samegene.columns = ["Alt_Splice","ExonTrans","ExonIntersect"]
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_unchange_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_intron_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_exon_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = alt_splice_change_data.fillna(0)
    alt_splice_change_data["Report"] = "YES"
    alt_splice_change_data["TranscriptDetail"] = "YES"
    #outdataframe = outdataframe.head(10)
    for i in alt_splice_change_data.index:
        UnChangeTrans = re.split("\|",str(alt_splice_change_data.loc[i,"UnChangeTrans"]))
        IntronTrans = re.split("\|",str(alt_splice_change_data.loc[i,"IntronTrans"]))
        ExonTrans = re.split("\|",str(alt_splice_change_data.loc[i,"ExonTrans"]))
        UnChangeInfo = alt_splice_change_data.loc[i,"UnChangeInfo"]
        ChangeInfo = alt_splice_change_data.loc[i,"ChangeInfo"]
        common_elements = set(UnChangeTrans) & set(IntronTrans) & set(ExonTrans)
        common_elements = list(common_elements)
        if "0" in common_elements:
            common_elements.remove("0") 
        if len(common_elements) > 0:
            if UnChangeInfo == 1:
                if ChangeInfo == 0:
                    Trans = "YES"
                else:
                    Trans = "NO"
            else:
                Trans = "NO"
            #Trans = "YES"
            TransDetail = "|".join(common_elements)
        else:
            Trans = "NO"
            TransDetail = "-"
        alt_splice_change_data.loc[i,"Report"] = Trans
        alt_splice_change_data.loc[i,"TranscriptDetail"] = TransDetail
    return alt_splice_change_data
    ########################################
########################################
########################################
def filter_alt5(alt_splice_unchange,alt_splice_change,alt_splice_inner_intron,alt_splice_inner_exon):
    alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
    alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
    alt_splice_inner_intron_data = pd.read_csv(alt_splice_inner_intron, sep="\t", header=None, skiprows=0)
    alt_splice_inner_exon_data = pd.read_csv(alt_splice_inner_exon, sep="\t", header=None, skiprows=0)
    ########################################
    ########################################
    #print(alt_splice_unchange_data)
    alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
    alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[8].str.split(":",expand=True)
    alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
    alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    ########################################
    ########################################
    #print(alt_splice_change_data)
    alt_splice_change_data = alt_splice_change_data[[3,10]]
    alt_splice_change_data = alt_splice_change_data.drop_duplicates()
    #alt_splice_change_data.columns = ["Alt_Splice","ChangeSite"]
    #print(alt_splice_change_data)
    ########################################
    ########################################
    #print(alt_splice_inner_intron_data)
    alt_splice_inner_intron_data["len"] = alt_splice_inner_intron_data[7] - alt_splice_inner_intron_data[6]
    alt_splice_inner_intron_data["intersect_ratio"] = alt_splice_inner_intron_data[10] / alt_splice_inner_intron_data["len"]
    alt_splice_inner_intron_data["gene_Examined"] = alt_splice_inner_intron_data[3].str.split(":",expand=True)[0]
    alt_splice_inner_intron_data[["gene_anno","trans_anno"]] = alt_splice_inner_intron_data[8].str.split(":",expand=True)
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data[alt_splice_inner_intron_data["gene_Examined"]==alt_splice_inner_intron_data["gene_anno"]]
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene[alt_splice_inner_intron_data_samegene["intersect_ratio"]==1]
    alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    ########################################
    ########################################
    #print(alt_splice_inner_exon_data)
    alt_splice_inner_exon_data["len"] = alt_splice_inner_exon_data[7] - alt_splice_inner_exon_data[6]
    alt_splice_inner_exon_data["intersect_ratio"] = alt_splice_inner_exon_data[10] / alt_splice_inner_exon_data["len"]
    alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data["intersect_ratio"]<1]
    alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data[10]>=2]
    alt_splice_inner_exon_data["gene_Examined"] = alt_splice_inner_exon_data[3].str.split(":",expand=True)[0]
    alt_splice_inner_exon_data[["gene_anno","trans_anno"]] = alt_splice_inner_exon_data[8].str.split(":",expand=True)
    alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data[alt_splice_inner_exon_data["gene_Examined"]==alt_splice_inner_exon_data["gene_anno"]]
    alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data_samegene.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    alt_splice_inner_exon_data_samegene["ExonIntersect"] = 2
    ########################################
    ########################################
    alt_splice_change_data.columns = ["Alt_Splice","ChangeInfo"]
    alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
    alt_splice_inner_intron_data_samegene.columns = ["Alt_Splice","IntronIntersect","IntronTrans"]
    alt_splice_inner_exon_data_samegene.columns = ["Alt_Splice","ExonTrans","ExonIntersect"]
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_unchange_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_intron_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_exon_data_samegene,on='Alt_Splice',how='left')
    alt_splice_change_data = alt_splice_change_data.fillna(0)
    alt_splice_change_data["Report"] = "YES"
    alt_splice_change_data["TranscriptDetail"] = "YES"
    #outdataframe = outdataframe.head(10)
    for i in alt_splice_change_data.index:
        UnChangeTrans = re.split("\|",str(alt_splice_change_data.loc[i,"UnChangeTrans"]))
        IntronTrans = re.split("\|",str(alt_splice_change_data.loc[i,"IntronTrans"]))
        ExonTrans = re.split("\|",str(alt_splice_change_data.loc[i,"ExonTrans"]))
        UnChangeInfo = alt_splice_change_data.loc[i,"UnChangeInfo"]
        ChangeInfo = alt_splice_change_data.loc[i,"ChangeInfo"]
        common_elements = set(UnChangeTrans) & set(IntronTrans) & set(ExonTrans)
        common_elements = list(common_elements)
        if "0" in common_elements:
            common_elements.remove("0") 
        if len(common_elements) > 0:
            if UnChangeInfo == 1:
                if ChangeInfo == 0:
                    Trans = "YES"
                else:
                    Trans = "NO"
            else:
                Trans = "NO"
            TransDetail = "|".join(common_elements)
        else:
            Trans = "NO"
            TransDetail = "-"
        alt_splice_change_data.loc[i,"Report"] = Trans
        alt_splice_change_data.loc[i,"TranscriptDetail"] = TransDetail
    ########################################
    return alt_splice_change_data
    ########################################
########################################
########################################
def filter_altC(alt_splice_unchange,alt_splice_change,alt_splice_last_exon,alt_splice_exon):
    alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
    alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
    alt_splice_last_exon_data = pd.read_csv(alt_splice_last_exon, sep="\t", header=None, skiprows=0)
    alt_splice_exon_data = pd.read_csv(alt_splice_exon, sep="\t", header=None, skiprows=0)
    #########################################################################################
    outdataframe = alt_splice_unchange_data[[3]]
    outdataframe = outdataframe.drop_duplicates()
    #########################################################################################
    #########################################################################################
    #print(alt_splice_unchange_data)
    alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
    alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[8].str.split(":",expand=True)
    alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
    alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(alt_splice_change_data)
    #########################################################################################
    alt_splice_change_data["gene_Examined"] = alt_splice_change_data[3].str.split(":",expand=True)[0]
    alt_splice_change_data[["gene_anno","trans_anno"]] = alt_splice_change_data[8].str.split(":",expand=True)
    alt_splice_change_data_samegene = alt_splice_change_data[alt_splice_change_data["gene_Examined"]==alt_splice_change_data["gene_anno"]]
    alt_splice_change_data_samegene = alt_splice_change_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #########################################################################################
    #print(alt_splice_unchange_data_samegene)
    #print(alt_splice_change_data_samegene)
    #########################################################################################
    #print(alt_splice_last_exon_data)
    alt_splice_last_exon_data["len"] = alt_splice_last_exon_data[7] - alt_splice_last_exon_data[6]
    alt_splice_last_exon_data["intersect_ratio"] = alt_splice_last_exon_data[10] / alt_splice_last_exon_data["len"]
    alt_splice_last_exon_data["gene_Examined"] = alt_splice_last_exon_data[3].str.split(":",expand=True)[0]
    alt_splice_last_exon_data[["gene_anno","trans_anno"]] = alt_splice_last_exon_data[8].str.split(":",expand=True)
    alt_splice_last_exon_data_samegene = alt_splice_last_exon_data[alt_splice_last_exon_data["gene_Examined"]==alt_splice_last_exon_data["gene_anno"]]
    alt_splice_last_exon_data_samegene = alt_splice_last_exon_data_samegene[alt_splice_last_exon_data_samegene["intersect_ratio"]==1]
    alt_splice_last_exon_data_samegene = alt_splice_last_exon_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #########################################################################################
    #print(alt_splice_exon_data)
    alt_splice_exon_data["gene_Examined"] = alt_splice_exon_data[3].str.split(":",expand=True)[0]
    alt_splice_exon_data[["gene_anno","trans_anno"]] = alt_splice_exon_data[8].str.split(":",expand=True)
    alt_splice_exon_data = alt_splice_exon_data[alt_splice_exon_data["gene_Examined"]==alt_splice_exon_data["gene_anno"]]
    alt_splice_exon_data = alt_splice_exon_data[alt_splice_exon_data[10]>1]
    alt_splice_exon_data = alt_splice_exon_data.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(alt_splice_exon_data)
    #########################################################################################
    #print(outdataframe)
    outdataframe.columns = ["Alt_Splice"]
    alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
    alt_splice_change_data_samegene.columns = ["Alt_Splice","ChangeInfo","ChangeTrans"]
    alt_splice_last_exon_data_samegene.columns = ["Alt_Splice","LastExonInfo","LastExonTrans"]
    alt_splice_exon_data.columns = ["Alt_Splice","ExonTrans"]
    outdataframe = pd.merge(outdataframe,alt_splice_unchange_data_samegene,on="Alt_Splice",how='left')
    outdataframe = pd.merge(outdataframe,alt_splice_change_data_samegene,on="Alt_Splice",how='left')
    outdataframe = pd.merge(outdataframe,alt_splice_last_exon_data_samegene,on="Alt_Splice",how='left')
    outdataframe = pd.merge(outdataframe,alt_splice_exon_data,on="Alt_Splice",how='left')
    outdataframe = outdataframe.fillna(0)
    #print(outdataframe)
    #########################################################################################
    outdataframe["Report"] = "NO"
    outdataframe["TranscriptDetail"] = "-"
    for i in outdataframe.index:
        Trans = "NO"
        TransDetail = "-"
        UnChangeTrans = re.split("\|",str(outdataframe.loc[i,"UnChangeTrans"]))
        UnChangeInfo = outdataframe.loc[i,"UnChangeInfo"]
        ChangeTrans = re.split("\|",str(outdataframe.loc[i,"ChangeTrans"]))
        ChangeInfo = outdataframe.loc[i,"ChangeInfo"]
        LastExonTrans = re.split("\|",str(outdataframe.loc[i,"LastExonTrans"]))
        LastExonInfo = outdataframe.loc[i,"LastExonInfo"]
        ExonTrans = re.split("\|",str(outdataframe.loc[i,"ExonTrans"]))
        if UnChangeInfo == 1:
            if ChangeInfo == 0:
                if LastExonInfo == 1:
                    common_elements = set(UnChangeTrans) & set(LastExonTrans)
                    if len(common_elements) > 0:
                        diff_elements = set(ChangeTrans) - common_elements
                        if len(diff_elements) > 0:
                            Trans = "YES"
                            TransDetail = "-"
                if LastExonInfo == 0:
                    common_elements = set(UnChangeTrans) & set(LastExonTrans) & set(ChangeTrans)
                    if len(common_elements) > 0:
                        Trans = "YES"
                        TransDetail = "-"
        outdataframe.loc[i,"Report"] = Trans
        outdataframe.loc[i,"TranscriptDetail"] = TransDetail
    return outdataframe
########################################
########################################
def filter_altPromoter(alt_splice_change,alt_splice_unchange,gene_first_exon):
    alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
    alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
    gene_first_exon_data = pd.read_csv(gene_first_exon, sep="\t", header=None, skiprows=0)
    #########################################################################################
    #print(alt_splice_unchange_data)
    #print(alt_splice_change_data)
    #print(gene_first_exon_data)
    #########################################################################################
    outdataframe = alt_splice_unchange_data[[3,4]]
    outdataframe = outdataframe.drop_duplicates()
    outdataframe.columns = ["Alt_Splice","pos_igv_bak"]
    #########################################################################################
    alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
    alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[8].str.split(":",expand=True)
    alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
    alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #########################################################################################
    alt_splice_change_data["gene_Examined"] = alt_splice_change_data[3].str.split(":",expand=True)[0]
    alt_splice_change_data[["gene_anno","trans_anno"]] = alt_splice_change_data[8].str.split(":",expand=True)
    alt_splice_change_data_samegene = alt_splice_change_data[alt_splice_change_data["gene_Examined"]==alt_splice_change_data["gene_anno"]]
    alt_splice_change_data_samegene = alt_splice_change_data_samegene.groupby([3,10])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
    #print(alt_splice_change_data)
    #print(alt_splice_unchange_data_samegene)
    #print(alt_splice_change_data_samegene)
    #########################################################################################
    gene_first_exon_data[["gene_anno","trans"]] = gene_first_exon_data[3].str.split(":",expand=True)
    gene_first_exon_data = gene_first_exon_data[["gene_anno",0,1,2,4]]
    gene_first_exon_data.columns = ["gene_anno","chr","start","end","strand"]
    #print(gene_first_exon_data)
    #########################################################################################
    alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
    alt_splice_change_data_samegene.columns = ["Alt_Splice","ChangeInfo","ChangeTrans"]
    outdataframe["gene_anno"] = outdataframe["Alt_Splice"].str.split(":",expand=True)[0]
    outdataframe = pd.merge(outdataframe,gene_first_exon_data,on="gene_anno",how='left')
    #print(outdataframe)
    outdataframe = pd.merge(outdataframe,alt_splice_unchange_data_samegene,on="Alt_Splice",how='left')
    outdataframe = pd.merge(outdataframe,alt_splice_change_data_samegene,on="Alt_Splice",how='left')
    outdataframe = outdataframe.fillna(0)
    #########################################################################################
    outdataframe[["chr_exam","start_exam","end_exam"]] = outdataframe["pos_igv_bak"].str.split(":|-",expand=True)
    outdataframe["Distance"] = "NO"
    outdataframe["Report"] = "NO"
    #print(outdataframe)
    for i in outdataframe.index:
        Trans = "NO"
        distance = "NO"
        UnChangeTrans = re.split("\|",str(outdataframe.loc[i,"UnChangeTrans"]))
        UnChangeInfo = outdataframe.loc[i,"UnChangeInfo"]
        ChangeTrans = re.split("\|",str(outdataframe.loc[i,"ChangeTrans"]))
        ChangeInfo = outdataframe.loc[i,"ChangeInfo"]
        strand = outdataframe.loc[i,"strand"]
        start = outdataframe.loc[i,"start"]
        end = outdataframe.loc[i,"end"]
        start_exam = outdataframe.loc[i,"start_exam"]
        end_exam = outdataframe.loc[i,"end_exam"]
        if strand == "+":
            distance = int(end) - int(start_exam)
        else:
            distance = int(end_exam) - int(start)
        if distance > 0:
            distance = "YES"
        if UnChangeInfo == 1:
            if ChangeInfo == 0:
                if distance == "YES":
                    Trans = "YES"
                else:
                    Trans = "NO"
        outdataframe.loc[i,"Report"] = Trans
        outdataframe.loc[i,"Distance"] = distance
    #########################################################################################
    return outdataframe
    #########################################################################################
########################################
########################################
def filter_altTransSplice(pos3_intersect_file,pos5_intersect_file):
    #########################################################################################
    pos3_intersect_data = pd.read_csv(pos3_intersect_file, sep="\t", header=None, skiprows=0)
    pos5_intersect_data = pd.read_csv(pos5_intersect_file, sep="\t", header=None, skiprows=0)
    print(pos3_intersect_data)
    print(pos5_intersect_data)
    #########################################################################################
    #pos3_intersect_data["gene_Examined"] = pos3_intersect_data[3].str.split(":",expand=True)[0]
    pos3_intersect_data = pos3_intersect_data.groupby([3])[8].agg(lambda x: '|'.join(x)).reset_index()
    pos5_intersect_data = pos5_intersect_data.groupby([3])[8].agg(lambda x: '|'.join(x)).reset_index()
    intersect_data_merge = pd.merge(pos3_intersect_data,pos5_intersect_data,on=3,how='left')
    intersect_data_merge.columns = ["Alt_Splice","pos3_gene","pos5_gene"]
    intersect_data_merge["Report"] = "-"
    for i in intersect_data_merge.index:
        Alt_Splice = intersect_data_merge.loc[i,"Alt_Splice"]
        pos3_gene = re.split("\|",str(intersect_data_merge.loc[i,"pos3_gene"]))
        pos5_gene = re.split("\|",str(intersect_data_merge.loc[i,"pos5_gene"]))
        common_elements = set(pos3_gene) & set(pos5_gene)
        if len(common_elements) <= 0 & Alt_Splice.find("-ENSG")>=0:
            intersect_data_merge.loc[i,"Report"] = "YES"
        else:
            intersect_data_merge.loc[i,"Report"] = "NO"
    return intersect_data_merge
########################################
########################################
for splice in splice_type:
    if splice == "cassette-exon":
        leftpos_intersect_file = f"{analysis_dir}/08.Exon_Skipping_leftpos_intersect.bed"
        rightpos_intersect_file = f"{analysis_dir}/08.Exon_Skipping_rightpos_intersect.bed"
        inner_exon_intersect_file = f"{analysis_dir}/08.Exon_Skipping_inner_exon_intersect.bed"
        inner_intron_intersect_file = f"{analysis_dir}/08.Exon_Skipping_inner_intron_intersect.bed"
        outdataframe = filter_cassette_exon(leftpos_intersect_file,rightpos_intersect_file,inner_exon_intersect_file,inner_intron_intersect_file)
        outdataframe.to_csv(f"{analysis_dir}/09.Exon_Skipping_Result.txt",sep="\t",header=True,index=False)
    if splice == "alt3":
        alt_splice_unchange =  f"{analysis_dir}/08.Alt-3-5pos-intersect-3.bed"
        alt_splice_change =  f"{analysis_dir}/08.Alt-3-3pos-intersect-5.bed"
        alt_splice_inner_intron = f"{analysis_dir}/08.Alt-3-inner-intersect-intron.bed"
        alt_splice_inner_exon = f"{analysis_dir}/08.Alt-3-inner-intersect-exon.bed"
        outdataframe = filter_alt3(alt_splice_unchange,alt_splice_change,alt_splice_inner_intron,alt_splice_inner_exon)
        outdataframe.to_csv(f"{analysis_dir}/09.Alt_3_Result.txt",sep="\t",header=True,index=False)
    if splice == "alt5":
        alt_splice_unchange =  f"{analysis_dir}/08.Alt-5-3pos-intersect-5.bed"
        alt_splice_change =  f"{analysis_dir}/08.Alt-5-5pos-intersect-3.bed"
        alt_splice_inner_intron = f"{analysis_dir}/08.Alt-5-inner-intersect-intron.bed"
        alt_splice_inner_exon = f"{analysis_dir}/08.Alt-5-inner-intersect-exon.bed"
        outdataframe = filter_alt5(alt_splice_unchange,alt_splice_change,alt_splice_inner_intron,alt_splice_inner_exon)
        outdataframe.to_csv(f"{analysis_dir}/09.Alt_5_Result.txt",sep="\t",header=True,index=False)
    if splice == "altC":
        alt_splice_unchange =  f"{analysis_dir}/08.Alt-C-5pos-intersect-3.bed"
        alt_splice_change =  f"{analysis_dir}/08.Alt-C-3pos-intersect-5.bed"
        alt_splice_last_exon = f"{analysis_dir}/08.Alt-C-inner-intersect-lastExon.bed"
        alt_splice_exon = f"{analysis_dir}/08.Alt-C-inner-intersect-exon.bed"
        outdataframe = filter_altC(alt_splice_unchange,alt_splice_change,alt_splice_last_exon,alt_splice_exon)
        outdataframe.to_csv(f"{analysis_dir}/09.Alt_C_Result.txt",sep="\t",header=True,index=False)
    if splice == "altPromoter":
        alt_splice_change =  f"{analysis_dir}/08.Alt-Promoter-5pos-intersect-3.bed"
        alt_splice_unchange =  f"{analysis_dir}/08.Alt-Promoter-3pos-intersect-5.bed"
        outdataframe = filter_altPromoter(alt_splice_change,alt_splice_unchange,gene_first_exon)
        outdataframe.to_csv(f"{analysis_dir}/09.Alt_Promoter_Result.txt",sep="\t",header=True,index=False)
    if splice == "altTransSplice":
        pos3_intersect_file =  f"{analysis_dir}/08.Alt-TransSplice-3pos-gene.bed"
        pos5_intersect_file =  f"{analysis_dir}/08.Alt-TransSplice-5pos-gene.bed"
        outdataframe = filter_altTransSplice(pos3_intersect_file,pos5_intersect_file)
        outdataframe.to_csv(f"{analysis_dir}/09.Alt_TransSplice_Result.txt",sep="\t",header=True,index=False)
