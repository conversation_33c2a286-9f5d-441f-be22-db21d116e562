#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.IR_Diff_Top.py
@Time    :   2025/06/30 13:10:39
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
import os
outdir = f"/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result"
bedfiledir = f"/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/02.GTF_Bed_File_Process"
AnnoMergeFile = f"{outdir}/04.SampleSplice_IR_Anno_Merge_Correct_Output.txt"
outfile1 = f"{outdir}/05.SampleSplice_IR_Anno_Merge_inner.bed"
outfile2 = f"{outdir}/05.SampleSplice_IR_Anno_Merge_pos1.bed"
outfile3 = f"{outdir}/05.SampleSplice_IR_Anno_Merge_pos2.bed"

AnnoMergeData = pd.read_csv(AnnoMergeFile,sep="\t",header=0)
AS_Pair = AnnoMergeData[["SpliceInfo","Alt_Splice_x","Alt_Splice_y","Strand_x","Strand_y","pos_Examined_x","pos_Examined_y"]]
AS_Pair = AS_Pair.drop_duplicates()
AS_Pair["pos_IGV"] = "-"
for i in AS_Pair.index:
    Alt_Splice_x = AS_Pair.loc[i,"Alt_Splice_x"]
    Alt_Splice_y = AS_Pair.loc[i,"Alt_Splice_y"]
    strand_x = AS_Pair.loc[i,"Strand_x"]
    strand_y = AS_Pair.loc[i,"Strand_y"]
    IntronInfo = AS_Pair.loc[i,"SpliceInfo"]
    pos_Examined_x = AS_Pair.loc[i,"pos_Examined_x"]
    pos_Examined_y = AS_Pair.loc[i,"pos_Examined_y"]
    if strand_x != strand_y:
        print(f"链方向不一致：{Alt_Splice_x}和{Alt_Splice_y}")
    else:
        if IntronInfo.find("ENSG") >= 0:
            pos_info_1 = re.split(":|-",pos_Examined_x)
            pos_info_2 = re.split(":|-",pos_Examined_y)
            if strand_x == "+":
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[1]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[1]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
            else:
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[2]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[2]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
        else:
            pos_info = re.split(":|-",pos_Examined_x)
            chr = pos_info[0]
            pos_1 = pos_info[1]
            pos_2 = pos_info[2]
            if int(pos_1) > int(pos_2):
                pos = f"{chr}:{pos_2}-{pos_1}"
            else:
                pos = f"{chr}:{pos_1}-{pos_2}"
        AS_Pair.loc[i,"pos_IGV"] = pos
AS_Pair[['chr', 'start', 'end']] = AS_Pair['pos_IGV'].str.split(':|-', expand=True)
AS_Pair["start_left"] = (AS_Pair["start"].astype(int)) - 1  
AS_Pair["end_left"] = (AS_Pair["start"].astype(int)) + 1  

AS_Pair["start_right"] = (AS_Pair["end"].astype(int)) - 1  
AS_Pair["end_right"] = (AS_Pair["end"].astype(int)) + 1  

AS_Pair[['chr', 'start', 'end', 'pos_IGV', "Alt_Splice_x"]].to_csv(outfile1,sep="\t",header=False,index=False)
AS_Pair[['chr', 'start_left', 'end_left', 'pos_IGV', "Alt_Splice_x"]].to_csv(outfile2,sep="\t",header=False,index=False)
AS_Pair[['chr', 'start_right', 'end_right', 'pos_IGV', "Alt_Splice_x"]].to_csv(outfile3,sep="\t",header=False,index=False)

intersect_Script1 = f"bedtools intersect -wao -a {outfile2} -b {bedfiledir}/exon.rightpos.bed > {outdir}/05.SampleSplice_IR_pos1.intersect.bed"
intersect_Script2 = f"bedtools intersect -wao -a {outfile3} -b {bedfiledir}/exon.leftpos.bed > {outdir}/05.SampleSplice_IR_pos2.intersect.bed"
intersect_Script3 = f"bedtools intersect -wao -a {outfile1} -b {bedfiledir}/intron.bed > {outdir}/05.SampleSplice_IR_intron.intersect.bed"
intersect_Script4 = f"bedtools intersect -wao -a {outfile1} -b {bedfiledir}/exon.bed > {outdir}/05.SampleSplice_IR_exon.intersect.bed"
intersect_Script5 = f"bedtools intersect -wao -a {outfile1} -b {bedfiledir}/exon.sort.merge.bed > {outdir}/05.SampleSplice_IR_exonMerge.intersect.bed"

os.system(intersect_Script1)
os.system(intersect_Script2)
os.system(intersect_Script3)
os.system(intersect_Script4)
os.system(intersect_Script5)
