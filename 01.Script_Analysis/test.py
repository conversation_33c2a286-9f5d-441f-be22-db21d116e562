#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   test.py
@Time    :   2025/07/16 10:07:05
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
file1 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/03.SampleSplice_IR.txt"
data1 = pd.read_csv(file1,sep="\t",header=0,skiprows=0)
data1 = data1[data1["EventAnnotation"]=="intron-retention"]
data1 = data1.sort_values(by=["Alt_Splice","SupportReads","SampleID"],ascending=[True,False,True])
data1 = data1.drop_duplicates(subset=["Alt_Splice"], keep="first")

file2 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/02.Sample_Level_AltSplice.txt"
data2 = pd.read_csv(file2,sep="\t",header=0,skiprows=0)
data2 = data2.sort_values(by=["Alt_Splice","SupportReads","SampleID"],ascending=[True,False,True])
data2 = data2[data2["EventAnnotation"]=="intron-retention"]
print(data2)
data2 = data2.drop_duplicates(subset=["Alt_Splice"], keep="first")

data = pd.merge(data1,data2,how='inner',on='Alt_Splice')

data_test = data[data["SampleID_x"]!=data["SampleID_y"]]
print(data1)
print(data2)
print(data)
print(data_test)