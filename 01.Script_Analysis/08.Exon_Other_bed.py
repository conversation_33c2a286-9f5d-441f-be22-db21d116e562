#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Exon_Skipping.py
@Time    :   2025/07/08 16:52:45
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
import os
outdir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result"
bedfiledir = f"/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/02.GTF_Bed_File_Process"
pair_info_File = f"{outdir}/07.SampleSplice_Exon_Anno.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[pair_info_data["EventAnnotation"]!="intron-retention"]
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
#pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = f"{outdir}/02.Sample_Level_AltSplice.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = ID_Data[ID_Data["EventAnnotation"]!="intron-retention"]
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
ID_Data[["chr"]] = "-"
ID_Data[["start"]] = "-"
ID_Data[["end"]] = "-"
ID_Data[["start_5"]] = "-"
ID_Data[["end_5"]] = "-"
ID_Data[["start_3"]] = "-"
ID_Data[["end_3"]] = "-"

for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
        start = pos_2
        end = pos_1
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
        start = pos_1
        end = pos_2
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
    ID_Data.loc[i,"start"] = start
    ID_Data.loc[i,"end"] = end
    start_5 = int(pos_1) - 1
    end_5 = int(pos_1)
    start_3 = int(pos_2) - 1
    end_3 = int(pos_2)
    ID_Data.loc[i,"chr"] = chr
    ID_Data.loc[i,"start_5"] = start_5
    ID_Data.loc[i,"end_5"] = end_5
    ID_Data.loc[i,"start_3"] = start_3
    ID_Data.loc[i,"end_3"] = end_3

ID_Data["start_left"] = (ID_Data["start"].astype(int)) - 1
ID_Data["start_right"] = (ID_Data["end"].astype(int)) - 1  
ID_Data = ID_Data[["chr","start","end","start_5","end_5","start_3","end_3","start_left","start_right","Alt_Splice","pos_Examined","pos_IGV","EventAnnotation"]]
ID_Data = ID_Data.drop_duplicates()
#############################################################
#############################################################
#############################################################
cassette_data = ID_Data[ID_Data["EventAnnotation"]=="cassette-exon"]
cassette_out1 = f"{outdir}/08.Exon_Skipping_leftpos.bed"
cassette_out2 = f"{outdir}/08.Exon_Skipping_innerpos.bed"
cassette_out3 = f"{outdir}/08.Exon_Skipping_rightpos.bed"
cassette_data[["chr","start_left", "start", "Alt_Splice","pos_IGV"]].to_csv(cassette_out1,sep="\t",header=False,index=False)
cassette_data[["chr","start", "start_right", "Alt_Splice","pos_IGV"]].to_csv(cassette_out2,sep="\t",header=False,index=False)
cassette_data[["chr","start_right", "end", "Alt_Splice","pos_IGV"]].to_csv(cassette_out3,sep="\t",header=False,index=False)
#############################################################
cassette_Script1 = f"bedtools intersect -wao -a {cassette_out1} -b {bedfiledir}/exon.rightpos.bed > {outdir}/08.Exon_Skipping_leftpos_intersect.bed"
cassette_Script2 = f"bedtools intersect -wao -a {cassette_out3} -b {bedfiledir}/exon.leftpos.bed > {outdir}/08.Exon_Skipping_rightpos_intersect.bed"
cassette_Script3 = f"bedtools intersect -wao -a {cassette_out2} -b {bedfiledir}/exon.bed > {outdir}/08.Exon_Skipping_inner_exon_intersect.bed"
cassette_Script4 = f"bedtools intersect -wao -a {cassette_out2} -b {bedfiledir}/intron.bed > {outdir}/08.Exon_Skipping_inner_intron_intersect.bed"
os.system(cassette_Script1)
os.system(cassette_Script2)
os.system(cassette_Script3)
os.system(cassette_Script4)
#############################################################
#############################################################
#############################################################
ID_Data_3 = ID_Data[ID_Data["EventAnnotation"]=="alt-3'"]
ID_Data_3_out1 = f"{outdir}/08.Alt-3-5pos.bed"
ID_Data_3_out2 = f"{outdir}/08.Alt-3-3pos.bed"
ID_Data_3_out3 = f"{outdir}/08.Alt-3-inner.bed"
ID_Data_3[["chr","start_5", "end_5", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_3_out1,sep="\t",header=False,index=False)
ID_Data_3[["chr","start_3", "end_3", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_3_out2,sep="\t",header=False,index=False)
ID_Data_3[["chr","start", "end", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_3_out3,sep="\t",header=False,index=False)
#############################################################
alt3_Script1 = f"bedtools intersect -wao -a {ID_Data_3_out1} -b {bedfiledir}/exon-3-start.bed > {outdir}/08.Alt-3-5pos-intersect-3.bed"
alt3_Script2 = f"bedtools intersect -wao -a {ID_Data_3_out2} -b {bedfiledir}/exon-5-start.bed > {outdir}/08.Alt-3-3pos-intersect-5.bed"
alt3_Script3 = f"bedtools intersect -wao -a {ID_Data_3_out3} -b {bedfiledir}/intron.bed > {outdir}/08.Alt-3-inner-intersect-intron.bed"
alt3_Script4 = f"bedtools intersect -wao -a {ID_Data_3_out3} -b {bedfiledir}/exon.bed > {outdir}/08.Alt-3-inner-intersect-exon.bed"
os.system(alt3_Script1)
os.system(alt3_Script2)
os.system(alt3_Script3)
os.system(alt3_Script4)
#############################################################
#############################################################
#############################################################
ID_Data_5 = ID_Data[ID_Data["EventAnnotation"]=="alt-5'"]
ID_Data_5_out1 = f"{outdir}/08.Alt-5-5pos.bed"
ID_Data_5_out2 = f"{outdir}/08.Alt-5-3pos.bed"
ID_Data_5_out3 = f"{outdir}/08.Alt-5-inner.bed"
ID_Data_5[["chr","start_5", "end_5", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_5_out1,sep="\t",header=False,index=False)
ID_Data_5[["chr","start_3", "end_3", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_5_out2,sep="\t",header=False,index=False)
ID_Data_5[["chr","start", "end", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_5_out3,sep="\t",header=False,index=False)
alt5_Script1 = f"bedtools intersect -wao -a {ID_Data_5_out1} -b {bedfiledir}/exon-3-start.bed > {outdir}/08.Alt-5-5pos-intersect-3.bed"
alt5_Script2 = f"bedtools intersect -wao -a {ID_Data_5_out2} -b {bedfiledir}/exon-5-start.bed > {outdir}/08.Alt-5-3pos-intersect-5.bed"
alt5_Script3 = f"bedtools intersect -wao -a {ID_Data_5_out3} -b {bedfiledir}/intron.bed > {outdir}/08.Alt-5-inner-intersect-intron.bed"
alt5_Script4 = f"bedtools intersect -wao -a {ID_Data_5_out3} -b {bedfiledir}/exon.bed > {outdir}/08.Alt-5-inner-intersect-exon.bed"
os.system(alt5_Script1)
os.system(alt5_Script2)
os.system(alt5_Script3)
os.system(alt5_Script4)
#############################################################
#############################################################
#############################################################
ID_Data_altC = ID_Data[ID_Data["EventAnnotation"]=="alt-C-term"]
ID_Data_altC_out1 = f"{outdir}/08.Alt-C-5pos.bed"
ID_Data_altC_out2 = f"{outdir}/08.Alt-C-3pos.bed"
ID_Data_altC_out3 = f"{outdir}/08.Alt-C-inner.bed"
ID_Data_altC[["chr","start_5", "end_5", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altC_out1,sep="\t",header=False,index=False)
ID_Data_altC[["chr","start_3", "end_3", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altC_out2,sep="\t",header=False,index=False)
ID_Data_altC[["chr","start", "end", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altC_out3,sep="\t",header=False,index=False)
altC_Script1 = f"bedtools intersect -wao -a {ID_Data_altC_out1} -b {bedfiledir}/exon-3-start.bed > {outdir}/08.Alt-C-5pos-intersect-3.bed"
altC_Script2 = f"bedtools intersect -wao -a {ID_Data_altC_out2} -b {bedfiledir}/exon-5-start.bed > {outdir}/08.Alt-C-3pos-intersect-5.bed"
altC_Script3 = f"bedtools intersect -wao -a {ID_Data_altC_out3} -b {bedfiledir}/exon_transcript_last-exon.bed > {outdir}/08.Alt-C-inner-intersect-lastExon.bed"
altC_Script4 = f"bedtools intersect -wao -a {ID_Data_altC_out3} -b {bedfiledir}/exon.bed > {outdir}/08.Alt-C-inner-intersect-exon.bed"
os.system(altC_Script1)
os.system(altC_Script2)
os.system(altC_Script3)
os.system(altC_Script4)
#############################################################
#############################################################
#############################################################
ID_Data_altP = ID_Data[ID_Data["EventAnnotation"]=="altPromoter"]
ID_Data_altP_out1 = f"{outdir}/08.Alt-P-5pos.bed"
ID_Data_altP_out2 = f"{outdir}/08.Alt-P-3pos.bed"
ID_Data_altP_out3 = f"{outdir}/08.Alt-P-inner.bed"
ID_Data_altP[["chr","start_5", "end_5", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altP_out1,sep="\t",header=False,index=False)
ID_Data_altP[["chr","start_3", "end_3", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altP_out2,sep="\t",header=False,index=False)
ID_Data_altP[["chr","start", "end", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altP_out3,sep="\t",header=False,index=False)
altP_Script1 = f"bedtools intersect -wao -a {ID_Data_altP_out1} -b {bedfiledir}/exon-3-start.bed > {outdir}/08.Alt-Promoter-5pos-intersect-3.bed"
altP_Script2 = f"bedtools intersect -wao -a {ID_Data_altP_out2} -b {bedfiledir}/exon-5-start.bed > {outdir}/08.Alt-Promoter-3pos-intersect-5.bed"
os.system(altP_Script1)
os.system(altP_Script2)
#############################################################
#############################################################
#############################################################
ID_Data_altT = ID_Data[ID_Data["EventAnnotation"]=="trans-splicing"]
ID_Data_altT_out1 = f"{outdir}/08.Alt-T-5pos.bed"
ID_Data_altT_out2 = f"{outdir}/08.Alt-T-3pos.bed"
ID_Data_altT[["chr","start_5", "end_5", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altT_out1,sep="\t",header=False,index=False)
ID_Data_altT[["chr","start_3", "end_3", "Alt_Splice","pos_IGV"]].to_csv(ID_Data_altT_out2,sep="\t",header=False,index=False)
altT_Script1 = f"bedtools intersect -wao -a {ID_Data_altT_out1} -b {bedfiledir}/gene.bed > {outdir}/08.Alt-TransSplice-5pos-gene.bed"
altT_Script2 = f"bedtools intersect -wao -a {ID_Data_altT_out2} -b {bedfiledir}/gene.bed > {outdir}/08.Alt-TransSplice-3pos-gene.bed"
os.system(altT_Script1)
os.system(altT_Script2)
#############################################################
#############################################################
#############################################################
