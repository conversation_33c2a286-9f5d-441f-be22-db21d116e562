#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.IR_Diff_Top.py
@Time    :   2025/06/30 13:10:39
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
outdir = f"/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result"

AnnoFile = f"{outdir}/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="intron-retention"]

anno_data_x = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","Cancer_SampleRatio","Cancer_dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data_x.columns = ["Alt_Splice_x","P_value_x","Cancer_Mean_x","Gtex_Mean_x","TCGA_Mean_x","Skin_Mean_x","Cancer_SampleRatio_x","Cancer_dataset_ratio_x","Skin_SampleRatio_x","Gtex_SampleRatio_x","TCGA_SampleRatio_x"]
anno_data_x["Diff_Gtex_x"] = anno_data_x["Cancer_Mean_x"] - anno_data_x["Gtex_Mean_x"]
anno_data_x["Diff_TCGA_x"] = anno_data_x["Cancer_Mean_x"] - anno_data_x["TCGA_Mean_x"]
anno_data_x["Diff_Skin_x"] = anno_data_x["Cancer_Mean_x"] - anno_data_x["Skin_Mean_x"]

anno_data_y = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","Cancer_SampleRatio","Cancer_dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data_y.columns = ["Alt_Splice_y","P_value_y","Cancer_Mean_y","Gtex_Mean_y","TCGA_Mean_y","Skin_Mean_y","Cancer_SampleRatio_y","Cancer_dataset_ratio_y","Skin_SampleRatio_y","Gtex_SampleRatio_y","TCGA_SampleRatio_y"]
anno_data_y["Diff_Gtex_y"] = anno_data_y["Cancer_Mean_y"] - anno_data_y["Gtex_Mean_y"]
anno_data_y["Diff_TCGA_y"] = anno_data_y["Cancer_Mean_y"] - anno_data_y["TCGA_Mean_y"]
anno_data_y["Diff_Skin_y"] = anno_data_y["Cancer_Mean_y"] - anno_data_y["Skin_Mean_y"]


intron_intersect_file = f"{outdir}/05.SampleSplice_IR_intron.intersect.bed"
intron_intersect_data = pd.read_csv(intron_intersect_file,sep="\t",header=None)
outdataframe = intron_intersect_data[[3]]
outdataframe = outdataframe.drop_duplicates()

intron_intersect_data["len"] = intron_intersect_data[2] - intron_intersect_data[1]
intron_intersect_data["intersect_intron"] = intron_intersect_data[10]/intron_intersect_data['len']
intron_intersect_data["gene_Examined"] = intron_intersect_data[4].str.split(":",expand=True)[0]
intron_intersect_data[["gene_anno","trans_anno"]] = intron_intersect_data[8].str.split(":",expand=True)
#print(intron_intersect_data)
intron_intersect_data_samegene = intron_intersect_data[intron_intersect_data["gene_Examined"]==intron_intersect_data["gene_anno"]]
intron_intersect_data_samegene = intron_intersect_data_samegene[intron_intersect_data_samegene["intersect_intron"]>0.95]
intron_intersect_data_samegene = intron_intersect_data_samegene.groupby([3])["gene_anno"].agg(lambda x: '|'.join(x)).reset_index()
print(intron_intersect_data_samegene)


exon_intersect_file = f"{outdir}/05.SampleSplice_IR_exon.intersect.bed"
exon_intersect_data = pd.read_csv(exon_intersect_file,sep="\t",header=None)
print(exon_intersect_data)
exon_intersect_data["len"] = exon_intersect_data[2] - exon_intersect_data[1]
exon_intersect_data["intersect_exon"] = exon_intersect_data[10]/exon_intersect_data['len']
exon_intersect_data["gene_Examined"] = exon_intersect_data[4].str.split(":",expand=True)[0]
exon_intersect_data[["gene_anno","trans_anno"]] = exon_intersect_data[8].str.split(":",expand=True)
exon_intersect_data_samegene = exon_intersect_data[exon_intersect_data["gene_Examined"]==exon_intersect_data["gene_anno"]]
exon_intersect_data_samegene = exon_intersect_data_samegene[exon_intersect_data_samegene["intersect_exon"]>0.05]
exon_intersect_data_samegene = exon_intersect_data_samegene.groupby([3])["gene_anno"].agg(lambda x: '|'.join(x)).reset_index()
print(exon_intersect_data_samegene)

exon_intersect_merge_file = f"{outdir}/05.SampleSplice_IR_exonMerge.intersect.bed"
exon_intersect_merge_data = pd.read_csv(exon_intersect_merge_file,sep="\t",header=None)
exon_intersect_merge_data["len"] = exon_intersect_merge_data[2] - exon_intersect_merge_data[1]
exon_intersect_merge_data["intersect_exonmerge"] = exon_intersect_merge_data[8]/exon_intersect_merge_data['len']
exon_intersect_merge_data = exon_intersect_merge_data.groupby([3])["intersect_exonmerge"].sum().reset_index()
print(exon_intersect_merge_data)
#print(exon_intersect_merge_data)

pos1_intersect_file = f"{outdir}/05.SampleSplice_IR_pos1.intersect.bed"
pos1_intersect_data = pd.read_csv(pos1_intersect_file,sep="\t",header=None)
pos1_intersect_data["gene_Examined"] = pos1_intersect_data[4].str.split(":",expand=True)[0]
pos1_intersect_data[["gene_anno","trans_anno"]] = pos1_intersect_data[8].str.split(":",expand=True)
pos1_intersect_data_samegene = pos1_intersect_data[pos1_intersect_data["gene_Examined"]==pos1_intersect_data["gene_anno"]]
pos1_intersect_data_samegene = pos1_intersect_data_samegene.groupby([3])["gene_anno"].agg(lambda x: '|'.join(x)).reset_index()
print(pos1_intersect_data_samegene)

pos2_intersect_file = f"{outdir}/05.SampleSplice_IR_pos2.intersect.bed"
pos2_intersect_data = pd.read_csv(pos2_intersect_file,sep="\t",header=None)
pos2_intersect_data["gene_Examined"] = pos2_intersect_data[4].str.split(":",expand=True)[0]
pos2_intersect_data[["gene_anno","trans_anno"]] = pos2_intersect_data[8].str.split(":",expand=True)
pos2_intersect_data_samegene = pos2_intersect_data[pos2_intersect_data["gene_Examined"]==pos2_intersect_data["gene_anno"]]
pos2_intersect_data_samegene = pos2_intersect_data_samegene.groupby([3])["gene_anno"].agg(lambda x: '|'.join(x)).reset_index()
print(pos2_intersect_data_samegene)


intron_intersect_data_samegene.columns = ["pos_IGV","IntronGene"]
exon_intersect_data_samegene.columns = ["pos_IGV","exonGene"]
pos1_intersect_data_samegene.columns = ["pos_IGV","Pos1Gene"]
pos2_intersect_data_samegene.columns = ["pos_IGV","Pos2Gene"]
exon_intersect_merge_data.columns = ["pos_IGV","intersect_exonmerge"]
outdataframe.columns = ["pos_IGV"]

outdataframe = pd.merge(outdataframe,intron_intersect_data_samegene,on="pos_IGV",how="left")
outdataframe = pd.merge(outdataframe,exon_intersect_data_samegene,on="pos_IGV",how="left")
outdataframe = pd.merge(outdataframe,pos1_intersect_data_samegene,on="pos_IGV",how="left")
outdataframe = pd.merge(outdataframe,pos2_intersect_data_samegene,on="pos_IGV",how="left")
outdataframe = pd.merge(outdataframe,exon_intersect_merge_data,on="pos_IGV",how="left")

outdataframe = outdataframe.fillna(0)

print(outdataframe)
outdataframe["Trans"] = "YES"
outdataframe["TransDetail"] = "YES"

for i in outdataframe.index:
    Pos1Trans = re.split("\|",str(outdataframe.loc[i,"Pos1Gene"]))
    Pos2Trans = re.split("\|",str(outdataframe.loc[i,"Pos2Gene"]))
    IntronTrans = re.split("\|",str(outdataframe.loc[i,"IntronGene"]))
    exonTrans = re.split("\|",str(outdataframe.loc[i,"exonGene"]))
    common_elements = set(Pos1Trans) & set(Pos2Trans) & set(IntronTrans)
    diff_elements = common_elements - set(exonTrans)
    diff_elements = list(diff_elements)
    if "0" in diff_elements:
        diff_elements.remove("0") 
    if len(diff_elements) > 0:
        Trans = "YES"
        TransDetail = "|".join(diff_elements)
    else:
        Trans = "NO"
        TransDetail = "-"
    outdataframe.loc[i,"Trans"] = Trans
    outdataframe.loc[i,"TransDetail"] = TransDetail
print(outdataframe.head(20))

AnnoMergeFile = f"{outdir}/04.SampleSplice_IR_Anno_Merge_Correct_Output.txt"
AnnoMergeData = pd.read_csv(AnnoMergeFile,sep="\t",header=0)
AnnoMergeData = pd.merge(AnnoMergeData,anno_data_x,how='left',on='Alt_Splice_x')
AnnoMergeData = pd.merge(AnnoMergeData,anno_data_y,how='left',on='Alt_Splice_y')
AnnoMergeData["P_value_mean"] = AnnoMergeData[["P_value_x","P_value_y"]].mean(axis=1)
AnnoMergeData["SupportReads_mean"] = AnnoMergeData[["SupportReads_x","SupportReads_y"]].mean(axis=1)
AnnoMergeData = AnnoMergeData.sort_values(by=["P_value_mean","Alt_Splice_x","SupportReads_mean"],ascending=[True,True,False])
AnnoMergeData["pos_IGV"] = "-"
AnnoMergeData["bam_Dir"] = "-"
print(AnnoMergeData)
for i in AnnoMergeData.index:
    Alt_Splice_x = AnnoMergeData.loc[i,"Alt_Splice_x"]
    Alt_Splice_y = AnnoMergeData.loc[i,"Alt_Splice_y"]
    SampleID_x = AnnoMergeData.loc[i,"SampleID_x"]
    SampleID = re.split("\.",SampleID_x)[0]
    #print(SampleID)
    bam_dir = f"http://192.168.2.253:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    strand_x = AnnoMergeData.loc[i,"Strand_x"]
    strand_y = AnnoMergeData.loc[i,"Strand_y"]
    IntronInfo = AnnoMergeData.loc[i,"SpliceInfo"]
    pos_Examined_x = AnnoMergeData.loc[i,"pos_Examined_x"]
    pos_Examined_y = AnnoMergeData.loc[i,"pos_Examined_y"]
    if strand_x != strand_y:
        print(f"链方向不一致：{Alt_Splice_x}和{Alt_Splice_y}")
    else:
        if IntronInfo.find("ENSG") >= 0:
            pos_info_1 = re.split(":|-",pos_Examined_x)
            pos_info_2 = re.split(":|-",pos_Examined_y)
            if strand_x == "+":
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[1]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[1]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
            else:
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[2]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[2]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
        else:
            pos_info = re.split(":|-",pos_Examined_x)
            chr = pos_info[0]
            pos_1 = pos_info[1]
            pos_2 = pos_info[2]
            if int(pos_1) > int(pos_2):
                pos = f"{chr}:{pos_2}-{pos_1}"
            else:
                pos = f"{chr}:{pos_1}-{pos_2}"
        AnnoMergeData.loc[i,"pos_IGV"] = pos
    AnnoMergeData.loc[i,"bam_Dir"] = bam_dir

AnnoMergeData = pd.merge(AnnoMergeData,outdataframe,how='left',on='pos_IGV')
AnnoMergeData = AnnoMergeData.drop_duplicates(subset=["Alt_Splice_x"],keep="first")
#AnnoMergeData = AnnoMergeData.head(300)
outfile  = f"{outdir}/06.SampleSplice_IR_Result.txt"
#print(anno_data)
#print(result)
print(AnnoMergeData)
AnnoMergeData.to_csv(outfile,sep="\t",header=True,index=False)
