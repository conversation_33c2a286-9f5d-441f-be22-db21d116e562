#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   06.IR_Pair_Find_3_Optimized.py
@Time    :   2025/06/27 12:03:46
<AUTHOR>   DingYu
@Version :   1.0 (Optimized)
@Contact :   <EMAIL>
'''
import pandas as pd
import numpy as np
from multiprocessing import Pool
import time

def process_intron_group(args):
    """
    处理单个内含子组的函数 - 保持原始逻辑完全一致
    """
    intron_name, id_data_sub_data = args

    results = []
    sample_id_list = list(set(id_data_sub_data["SampleID"]))

    for sample_id in sample_id_list:
        ID_Data_sub_sample = id_data_sub_data[id_data_sub_data["SampleID"] == sample_id]
        sample_splice = []  # 保持原始类型

        # 完全保持原始逻辑
        if intron_name.find("ENSG") >= 0:
            if len(ID_Data_sub_sample) == 2:
                sample_splice1 = ID_Data_sub_sample.iloc[[0]]
                sample_splice2 = ID_Data_sub_sample.iloc[[1]]
                sample_splice = pd.merge(sample_splice1, sample_splice2, how='inner', on='SpliceInfo')
        else:
            if len(ID_Data_sub_sample) == 1:
                sample_splice1 = ID_Data_sub_sample
                sample_splice2 = ID_Data_sub_sample
                sample_splice = pd.merge(sample_splice1, sample_splice2, how='inner', on='SpliceInfo')

        if len(sample_splice) > 0:
            results.append(sample_splice)

    return results

def main():
    """
    主函数：优化版本
    """
    print("开始处理IR配对数据 (优化版本)...")
    start_time = time.time()
    
    # 读取配对信息文件
    print("读取配对信息文件...")
    pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/03.SampleSplice_IR_Anno.txt"
    IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/02.Sample_Level_AltSplice.txt"
    out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/04.Analysis_Result/04.SampleSplice_IR_Anno_Merge.txt"

    pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
    pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "SpliceInfo","Strand"]]
    
    # 计算内含子计数
    print("计算内含子计数...")
    intron_counts = pair_info_data['SpliceInfo'].value_counts()
    pair_info_data['SpliceCount'] = pair_info_data['SpliceInfo'].map(intron_counts)
    
    # 保持原始逻辑处理IntronReserved列
    print("处理IntronReserved状态...")
    pair_info_data['SpliceReserved'] = "NO"
    for i in pair_info_data.index:
        IntronInfo = pair_info_data.loc[i, "SpliceInfo"]
        IntronCount = pair_info_data.loc[i, "SpliceCount"]
        if IntronInfo.find("JUNC") >= 0:
            IntronReserved = "YES"
        elif IntronInfo.find("ENSG") >= 0:
            if IntronCount > 2:
                IntronReserved = "NO"
            elif IntronCount == 2:
                IntronReserved = "YES"
            else:
                IntronReserved = "NO"
        else:
                IntronReserved = "NO"
        pair_info_data.loc[i, "SpliceReserved"] = IntronReserved
    
    # 读取IR文件并合并
    print("读取IR文件并合并数据...")
    ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
    ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')
    
    # 筛选保留的内含子信息
    pair_info_data = pair_info_data[pair_info_data["SpliceReserved"] == "YES"]
    print(pair_info_data)
    
    #IntronInfo_List = list(pair_info_data["IntronInfo"].values)
    IntronInfo_List = list(set(pair_info_data["SpliceInfo"].values))
    IntronNum = len(IntronInfo_List)
    print(f"需要处理的内含子数量: {IntronNum}")
    
    # 预处理：按IntronInfo分组数据
    print("预处理数据分组...")
    intron_groups = []
    for intron_name in IntronInfo_List:
        ID_Data_sub = ID_Data[ID_Data["SpliceInfo"] == intron_name]
        intron_groups.append((intron_name, ID_Data_sub))
    
    # 并行处理
    print(f"开始并行处理，使用 24 个进程...")
    with Pool(processes=24) as pool:
        all_results = pool.map(process_intron_group, intron_groups)
    
    # 合并所有结果 - 优化版本：收集所有DataFrame后一次性合并
    print("合并处理结果...")
    all_dataframes = []
    for results in all_results:
        for sample_splice in results:
            all_dataframes.append(sample_splice)

    # 一次性合并所有DataFrame，避免频繁的concat操作
    if len(all_dataframes) > 0:
        output_data = pd.concat(all_dataframes, ignore_index=True)
        print(f"成功合并 {len(all_dataframes)} 个数据片段")
    else:
        output_data = []
    
    # 保存结果 - 保持原始逻辑（原始脚本保存的是ID_Data）
    ID_Data.to_csv(out_File, header=True, index=False, sep="\t")

    # 额外保存正确的输出数据
    if len(output_data) > 0:
        output_correct_file = out_File.replace('.txt', '_Correct_Output.txt')
        output_data.to_csv(output_correct_file, header=True, index=False, sep="\t")
        print(f"正确的输出数据已保存到: {output_correct_file}")
        print(f"输出数据行数: {len(output_data)}")
    else:
        print("没有生成输出数据")
    
    end_time = time.time()
    print(f"处理完成！总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main()
