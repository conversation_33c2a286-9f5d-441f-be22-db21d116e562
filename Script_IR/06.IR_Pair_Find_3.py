#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   06.IR_Pair_Find_3.py
@Time    :   2025/06/27 12:03:46
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File,sep="\t",header=0,skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice","Raw_ID","IntronInfo"]]
intron_counts = pair_info_data['IntronInfo'].value_counts()
pair_info_data['IntronCount'] = pair_info_data['IntronInfo'].map(intron_counts)
pair_info_data['IntronReserved'] = "NO"
for i in pair_info_data.index:
    IntronInfo = pair_info_data.loc[i,"IntronInfo"]
    IntronCount = pair_info_data.loc[i,"IntronCount"]
    if IntronCount >2:
        IntronReserved = "NO"
    elif IntronInfo.find("ENSG") >= 0 and IntronCount ==2:
        IntronReserved = "YES"
    elif IntronInfo.find("JUNC") >= 0 and IntronCount ==1:
        IntronReserved = "YES"
    else:
        IntronReserved = "NO"
    pair_info_data.loc[i,"IntronReserved"] = IntronReserved
#print(pair_info_data.head(20))
IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR.txt"
ID_Data = pd.read_csv(IR_File,sep="\t",header=0,skiprows=0)
ID_Data = pd.merge(ID_Data,pair_info_data,how='inner',on='Alt_Splice')
pair_info_data = pair_info_data[pair_info_data["IntronReserved"]=="YES"]
#pair_info_data = pair_info_data.head(5)
print(pair_info_data)
IntronInfo_List = list(pair_info_data["IntronInfo"].values)
IntronNum = len(IntronInfo_List)
output_data = []
for Intron_Name in IntronInfo_List:
    i = 1
    pair_info_sub = pair_info_data[pair_info_data["IntronInfo"]==Intron_Name]
    ID_Data_sub = ID_Data[ID_Data["IntronInfo"]==Intron_Name]
    sample_id_list = list(set(ID_Data_sub["SampleID"]))
    for sample_id in sample_id_list:
        ID_Data_sub_sample = ID_Data_sub[ID_Data_sub["SampleID"]==sample_id]
        sample_splice = []
        if Intron_Name.find("ENSG") >= 0:
            if len(ID_Data_sub_sample) == 2:
                sample_splice1 = ID_Data_sub_sample.iloc[[0]]
                sample_splice2 = ID_Data_sub_sample.iloc[[1]]
                sample_splice = pd.merge(sample_splice1,sample_splice2,how='inner',on='IntronInfo')
        else:
            if len(ID_Data_sub_sample) == 1:
                sample_splice1 = ID_Data_sub_sample
                sample_splice2 = ID_Data_sub_sample
                sample_splice = pd.merge(sample_splice1,sample_splice2,how='inner',on='IntronInfo')
        if len(sample_splice) > 0:
            if len(output_data) ==0:
                output_data = sample_splice
            else:
                output_data = pd.concat([output_data,sample_splice],ignore_index=True)
    ratio = round(i / IntronNum,0)*100
    if ratio in [5,10,20,30,40,50,60,70,80,90,100]:
        print(f"已完成{ratio}")
    i = i + 1
out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/06.SampleSplice_IR_Anno_Merge.txt"
output_data.to_csv(out_File, header=True, index=False, sep="\t")
