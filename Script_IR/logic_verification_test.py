#!/usr/bin/env python3
"""
逻辑验证测试脚本
比较原始脚本和Turbo版本的处理结果是否一致
"""

import pandas as pd
import re
import os

def load_original_bed_file(sample_id, bed_dir):
    """原始脚本的bed文件加载逻辑"""
    intron_file = f"{bed_dir}/{sample_id}.Aligned.sortedByCoord.out__intronJunction.bed"
    junction_file = f"{bed_dir}/{sample_id}.Aligned.sortedByCoord.out__junction.bed"

    bed_data = {}

    # 加载intron junction文件
    if os.path.exists(intron_file):
        try:
            intron_data = pd.read_csv(intron_file, sep="\t", header=None)
            intron_data[["Gene","Exon","Pos"]] = intron_data[3].str.split('-|:', expand=True)
            bed_data['intron'] = intron_data
        except:
            bed_data['intron'] = pd.DataFrame()
    else:
        bed_data['intron'] = pd.DataFrame()

    # 加载junction文件
    if os.path.exists(junction_file):
        try:
            junction_data = pd.read_csv(junction_file, sep="\t", header=None, skiprows=1)
            junction_data[["Gene","Exon","Pos"]] = junction_data[3].str.split('-|:', expand=True)
            bed_data['junction'] = junction_data
        except:
            bed_data['junction'] = pd.DataFrame()
    else:
        bed_data['junction'] = pd.DataFrame()

    return bed_data

def load_turbo_bed_file(sample_id, bed_dir):
    """Turbo版本的bed文件加载逻辑"""
    intron_file = f"{bed_dir}/{sample_id}.Aligned.sortedByCoord.out__intronJunction.bed"
    junction_file = f"{bed_dir}/{sample_id}.Aligned.sortedByCoord.out__junction.bed"

    bed_data = {'intron_records': [], 'junction_records': []}

    # 加载intron文件
    if os.path.exists(intron_file):
        try:
            data = pd.read_csv(intron_file, sep="\t", header=None, engine='c')
            
            if not data.empty:
                split_data = data[3].str.split('-|:', expand=True)
                
                for i in range(len(data)):
                    try:
                        record = {
                            'chr': data.iloc[i, 0],
                            'raw_id': data.iloc[i, 3],
                            'support_reads': data.iloc[i, 4],
                            'gene': split_data.iloc[i, 0],
                            'exon': split_data.iloc[i, 1],
                            'pos': split_data.iloc[i, 2]
                        }
                        bed_data['intron_records'].append(record)
                    except:
                        continue
        except:
            pass

    # 加载junction文件
    if os.path.exists(junction_file):
        try:
            data = pd.read_csv(junction_file, sep="\t", header=None, skiprows=1, engine='c')
            
            if not data.empty:
                split_data = data[3].str.split('-|:', expand=True)
                
                for i in range(len(data)):
                    try:
                        record = {
                            'chr': data.iloc[i, 0],
                            'raw_id': data.iloc[i, 3],
                            'support_reads': data.iloc[i, 4],
                            'gene': split_data.iloc[i, 0],
                            'exon': split_data.iloc[i, 1],
                            'pos': split_data.iloc[i, 2]
                        }
                        bed_data['junction_records'].append(record)
                    except:
                        continue
        except:
            pass

    return bed_data

def process_original_logic(row, bed_data):
    """原始脚本的处理逻辑"""
    Alt_Splice = row["Alt_Splice"]
    pos_Examined = row["pos_Examined"]
    SampleInfo = row["SampleID"]
    SupportReads_1 = row["SupportReads"]

    gene_name = Alt_Splice.split(":")[0]
    pos_info = re.split(":|-", pos_Examined)
    SampleID = SampleInfo.split(".")[0]

    chr_info = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]

    if abs(int(pos_1) - int(pos_2)) == 1:
        # 使用intron数据
        SampleData = bed_data['intron']
        if SampleData.empty:
            return "-", "-"

        SampleData_1 = SampleData[
            (SampleData[0] == chr_info) &
            (SampleData["Pos"].isin([pos_1, pos_2]))
        ]

        if len(SampleData_1) > 1:
            SampleData_1 = SampleData_1[SampleData_1["Gene"] == gene_name]
    else:
        # 使用junction数据
        SampleData = bed_data['junction']
        if SampleData.empty:
            return "-", "-"

        SampleData_1 = SampleData[
            (SampleData[0] == chr_info) &
            (SampleData["Exon"] == pos_1) &
            (SampleData["Pos"] == pos_2)
        ]

        if len(SampleData_1) == 0:
            SampleData_1 = SampleData[
                (SampleData[0] == chr_info) &
                (SampleData["Exon"] == pos_2) &
                (SampleData["Pos"] == pos_1)
            ]

    SampleData_1 = SampleData_1[SampleData_1[4] == SupportReads_1]

    if len(SampleData_1) == 1:
        raw_id = SampleData_1[3].values[0]
        raw_gene_exon = f"{SampleData_1['Gene'].values[0]}:{SampleData_1['Exon'].values[0]}"
    else:
        raw_id = "-"
        raw_gene_exon = "-"

    return raw_id, raw_gene_exon

def process_turbo_logic(row, bed_data):
    """Turbo版本的处理逻辑"""
    Alt_Splice = row["Alt_Splice"]
    pos_Examined = row["pos_Examined"]
    SampleInfo = row["SampleID"]
    SupportReads_1 = row["SupportReads"]

    gene_name = Alt_Splice.split(":")[0]
    pos_info = re.split(":|-", pos_Examined)
    SampleID = SampleInfo.split(".")[0]

    chr_info = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]

    try:
        if abs(int(pos_1) - int(pos_2)) == 1:
            # intron查找逻辑
            intron_records = bed_data['intron_records']
            if not intron_records:
                return "-", "-"
            
            # 第一步筛选：chr匹配 AND pos in [pos_1, pos_2]
            matched_records = []
            for record in intron_records:
                if (record['chr'] == chr_info and 
                    record['pos'] in [pos_1, pos_2]):
                    matched_records.append(record)
            
            # 第二步筛选：如果多个结果，按gene筛选
            if len(matched_records) > 1:
                gene_matched = [r for r in matched_records if r['gene'] == gene_name]
                if gene_matched:
                    matched_records = gene_matched
            
            # 第三步筛选：按support_reads筛选
            final_matches = [r for r in matched_records if r['support_reads'] == SupportReads_1]
            
            if len(final_matches) == 1:
                record = final_matches[0]
                raw_id = record['raw_id']
                raw_gene_exon = f"{record['gene']}:{record['exon']}"
                return raw_id, raw_gene_exon
            else:
                return "-", "-"
                
        else:
            # junction查找逻辑
            junction_records = bed_data['junction_records']
            if not junction_records:
                return "-", "-"
            
            # 第一步：尝试 chr匹配 AND exon==pos_1 AND pos==pos_2
            matched_records = []
            for record in junction_records:
                if (record['chr'] == chr_info and 
                    record['exon'] == pos_1 and 
                    record['pos'] == pos_2):
                    matched_records.append(record)
            
            # 第二步：如果没找到，尝试 chr匹配 AND exon==pos_2 AND pos==pos_1
            if len(matched_records) == 0:
                for record in junction_records:
                    if (record['chr'] == chr_info and 
                        record['exon'] == pos_2 and 
                        record['pos'] == pos_1):
                        matched_records.append(record)
            
            # 第三步筛选：按support_reads筛选
            final_matches = [r for r in matched_records if r['support_reads'] == SupportReads_1]
            
            if len(final_matches) == 1:
                record = final_matches[0]
                raw_id = record['raw_id']
                raw_gene_exon = f"{record['gene']}:{record['exon']}"
                return raw_id, raw_gene_exon
            else:
                return "-", "-"
                
    except ValueError:
        return "-", "-"

def main():
    """测试逻辑一致性"""
    IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR.txt"
    bed_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/bed"

    print("开始逻辑验证测试...")
    
    # 读取测试数据（前1000行）
    test_data = pd.read_csv(IR_File, sep="\t", header=0, nrows=1000)
    print(f"测试数据: {len(test_data)} 行")
    
    # 获取需要的样本
    unique_samples = test_data["SampleID"].str.split(".").str[0].unique()[:5]  # 只测试前5个样本
    print(f"测试样本: {len(unique_samples)} 个")
    
    # 加载bed数据
    bed_cache_original = {}
    bed_cache_turbo = {}
    
    for sample_id in unique_samples:
        bed_cache_original[sample_id] = load_original_bed_file(sample_id, bed_dir)
        bed_cache_turbo[sample_id] = load_turbo_bed_file(sample_id, bed_dir)
    
    # 比较结果
    differences = 0
    total_tests = 0
    
    for i, row in test_data.iterrows():
        sample_id = row["SampleID"].split(".")[0]
        if sample_id not in bed_cache_original:
            continue
            
        try:
            # 原始逻辑结果
            orig_raw_id, orig_gene_exon = process_original_logic(row, bed_cache_original[sample_id])
            
            # Turbo逻辑结果
            turbo_raw_id, turbo_gene_exon = process_turbo_logic(row, bed_cache_turbo[sample_id])
            
            total_tests += 1
            
            # 比较结果
            if orig_raw_id != turbo_raw_id or orig_gene_exon != turbo_gene_exon:
                differences += 1
                if differences <= 10:  # 只显示前10个差异
                    print(f"差异 {differences}:")
                    print(f"  行 {i}: {row['Alt_Splice']} | {row['pos_Examined']}")
                    print(f"  原始: ({orig_raw_id}, {orig_gene_exon})")
                    print(f"  Turbo: ({turbo_raw_id}, {turbo_gene_exon})")
                    print()
        except Exception as e:
            print(f"处理行 {i} 时出错: {e}")
    
    print(f"测试完成!")
    print(f"总测试数: {total_tests}")
    print(f"差异数: {differences}")
    print(f"一致性: {(total_tests - differences) / total_tests * 100:.2f}%")
    
    if differences == 0:
        print("✅ 逻辑完全一致！")
    else:
        print("❌ 存在逻辑差异，需要进一步检查")

if __name__ == "__main__":
    main()
