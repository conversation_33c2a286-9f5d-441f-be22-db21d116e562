#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.IR_Diff_Top.py
@Time    :   2025/06/30 13:10:39
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="intron-retention"]
anno_data_x = anno_data[["Alt_Splice","All_adjPvalue"]]
anno_data_x.columns = ["Alt_Splice_x","P_value_x"]
anno_data_y = anno_data[["Alt_Splice","All_adjPvalue"]]
anno_data_y.columns = ["Alt_Splice_y","P_value_y"]

bed_intersect_file =  "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/07.SampleSplice_IR_Anno_Merge_Optimized_IGV_intersect.bed"
bed_intersect_data = pd.read_csv(bed_intersect_file,sep="\t",header=None)
bed_intersect_data = bed_intersect_data[bed_intersect_data[7].str.contains(':E', na=False)]
result = bed_intersect_data.groupby([0,1,2,3])[10].sum().reset_index()
result.columns = ["chr","start","end","pos_IGV","intersect"]
result["length"] = result["end"] - result["start"]
result["intersect_ratio"] = result["intersect"] / result["length"]
result = result[["pos_IGV","intersect_ratio"]]

AnnoMergeFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/06.SampleSplice_IR_Anno_Merge_Optimized_Correct_Output.txt"
AnnoMergeData = pd.read_csv(AnnoMergeFile,sep="\t",header=0)
AnnoMergeData = pd.merge(AnnoMergeData,anno_data_x,how='left',on='Alt_Splice_x')
AnnoMergeData = pd.merge(AnnoMergeData,anno_data_y,how='left',on='Alt_Splice_y')
AnnoMergeData["P_value_mean"] = AnnoMergeData[["P_value_x","P_value_y"]].mean(axis=1)
AnnoMergeData["SupportReads_mean"] = AnnoMergeData[["SupportReads_x","SupportReads_y"]].mean(axis=1)
AnnoMergeData = AnnoMergeData.sort_values(by=["P_value_mean","Alt_Splice_x","SupportReads_mean"],ascending=[True,True,False])
AnnoMergeData["pos_IGV"] = "-"
AnnoMergeData["bam_Dir"] = "-"
for i in AnnoMergeData.index:
    Alt_Splice_x = AnnoMergeData.loc[i,"Alt_Splice_x"]
    Alt_Splice_y = AnnoMergeData.loc[i,"Alt_Splice_y"]
    SampleID_x = AnnoMergeData.loc[i,"SampleID_x"]
    SampleID = re.split("\.",SampleID_x)[0]
    #print(SampleID)
    bam_dir = f"http://192.168.2.253:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    strand_x = AnnoMergeData.loc[i,"strand_x"]
    strand_y = AnnoMergeData.loc[i,"strand_y"]
    IntronInfo = AnnoMergeData.loc[i,"IntronInfo"]
    pos_Examined_x = AnnoMergeData.loc[i,"pos_Examined_x"]
    pos_Examined_y = AnnoMergeData.loc[i,"pos_Examined_y"]
    if strand_x != strand_y:
        print(f"链方向不一致：{Alt_Splice_x}和{Alt_Splice_y}")
    else:
        if IntronInfo.find("ENSG") >= 0:
            pos_info_1 = re.split(":|-",pos_Examined_x)
            pos_info_2 = re.split(":|-",pos_Examined_y)
            if strand_x == "+":
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[1]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[1]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
            else:
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[2]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[2]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
        else:
            pos_info = re.split(":|-",pos_Examined_x)
            chr = pos_info[0]
            pos_1 = pos_info[1]
            pos_2 = pos_info[2]
            if int(pos_1) > int(pos_2):
                pos = f"{chr}:{pos_2}-{pos_1}"
            else:
                pos = f"{chr}:{pos_1}-{pos_2}"
        AnnoMergeData.loc[i,"pos_IGV"] = pos
    AnnoMergeData.loc[i,"bam_Dir"] = bam_dir
AnnoMergeData = pd.merge(AnnoMergeData,result,how='left',on='pos_IGV')
AnnoMergeData = AnnoMergeData.drop_duplicates(subset=["Alt_Splice_x"],keep="first")
AnnoMergeData = AnnoMergeData.head(200)
outfile  = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/06.SampleSplice_IR_Anno_Merge_Optimized_Correct_Output-Top-dup.txt"
print(anno_data)
print(result)
print(AnnoMergeData)
AnnoMergeData.to_csv(outfile,sep="\t",header=True,index=False)
