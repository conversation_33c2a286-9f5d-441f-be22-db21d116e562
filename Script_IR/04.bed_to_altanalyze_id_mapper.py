#!/usr/bin/env python
"""
BED文件到AltAnalyze ID映射脚本
根据AltAnalyze原始逻辑，将bed文件中的junction ID转换为AltAnalyze_ID格式

使用方法:
python bed_to_altanalyze_id_mapper.py --bed_dir /path/to/bed/files --species Hs --output mapping_results.txt

作者: 基于AltAnalyze RNASeq.py逻辑实现
"""

import os
import sys
import argparse
import glob
from collections import defaultdict

class JunctionData:
    """模拟AltAnalyze的JunctionData类"""
    def __init__(self, chr, strand, exon1_stop, exon2_start, junction_id, biotype):
        self.chr = chr
        self.strand = strand
        self.exon1_stop = exon1_stop
        self.exon2_start = exon2_start
        self.junction_id = junction_id
        self.biotype = biotype
        self.gene_id = None
        self.unique_id = None
        self.exon_annotations = None
        self.left_exon_annotations = None
        self.right_exon_annotations = None
        self.secondary_gene_id = None
        self.trans_splicing = 'no'
        self.splice_sites_found = None
        
    def setGeneID(self, gene_id):
        self.gene_id = gene_id
        
    def GeneID(self):
        return self.gene_id
        
    def setUniqueID(self, uid):
        self.unique_id = uid
        
    def UniqueID(self):
        return self.unique_id
        
    def setExonAnnotations(self, annotations):
        self.exon_annotations = annotations
        
    def ExonAnnotations(self):
        return self.exon_annotations
        
    def setLeftExonAnnotations(self, left_annot):
        self.left_exon_annotations = left_annot
        
    def LeftExonAnnotations(self):
        return self.left_exon_annotations
        
    def setRightExonAnnotations(self, right_annot):
        self.right_exon_annotations = right_annot
        
    def RightExonAnnotations(self):
        return self.right_exon_annotations
        
    def setSecondaryGeneID(self, secondary_id):
        self.secondary_gene_id = secondary_id
        
    def SecondaryGeneID(self):
        return self.secondary_gene_id
        
    def setTransSplicing(self, trans):
        self.trans_splicing = trans
        
    def TransSplicing(self):
        return self.trans_splicing
        
    def setSpliceSitesFound(self, sites):
        self.splice_sites_found = sites
        
    def SpliceSitesFound(self):
        return self.splice_sites_found
        
    def BioType(self):
        return self.biotype
        
    def Chr(self):
        return self.chr
        
    def Strand(self):
        return self.strand
        
    def Exon1Stop(self):
        return self.exon1_stop
        
    def Exon2Start(self):
        return self.exon2_start
        
    def JunctionID(self):
        return self.junction_id

class ExonAnnotationsSimple:
    """模拟AltAnalyze的ExonAnnotationsSimple类"""
    def __init__(self, gene_id, exon_region_ids):
        self.gene_id = gene_id
        self.exon_region_ids = exon_region_ids
        
    def GeneID(self):
        return self.gene_id
        
    def ExonRegionIDs(self):
        return self.exon_region_ids

def parse_bed_file(bed_file_path):
    """
    解析bed文件，提取junction信息
    完全按照AltAnalyze的importBEDFile逻辑
    """
    junction_db = {}
    
    with open(bed_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            try:
                parts = line.split('\t')
                if len(parts) < 12:
                    continue
                    
                chr_name = parts[0]
                exon1_start = int(parts[1])
                exon2_stop = int(parts[2])
                junction_id = parts[3]
                reads = parts[4]
                strand = parts[5]
                lengths = parts[10]
                
                # 解析lengths字段
                length_parts = lengths.split(',')
                if len(length_parts) >= 2:
                    exon1_len = int(length_parts[0])
                    exon2_len = int(length_parts[1])
                else:
                    continue
                
                # 计算真实junction坐标 - 完全按照AltAnalyze逻辑
                if strand == '-':
                    if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
                        exon1_stop = exon1_start
                        exon2_start = exon2_stop
                    else:
                        exon1_stop = exon1_start + exon1_len
                        exon2_start = exon2_stop - exon2_len + 1
                    # 负链：交换exon顺序
                    a = (exon1_start, exon1_stop)
                    b = (exon2_start, exon2_stop)
                    exon1_stop, exon1_start = b
                    exon2_stop, exon2_start = a
                else:  # 正链
                    if (exon1_len + exon2_len) == 0:
                        exon1_stop = exon1_start
                        exon2_start = exon2_stop
                    else:
                        exon1_stop = exon1_start + exon1_len
                        exon2_start = exon2_stop - exon2_len + 1
                
                # 过滤条件：reads > 4
                if float(reads) > 4:
                    # 添加chr前缀（如果缺失）
                    if 'chr' not in chr_name:
                        chr_name = 'chr' + chr_name
                    
                    # 染色体标准化
                    if chr_name == 'chrM':
                        chr_name = 'chrMT'
                    
                    # 创建JunctionData对象
                    ji = JunctionData(chr_name, strand, exon1_stop, exon2_start, junction_id, 'junction')
                    key = (chr_name, exon1_stop, exon2_start)
                    junction_db[key] = ji
                    
            except (ValueError, IndexError) as e:
                print(f"Warning: 跳过无效行: {line[:50]}... 错误: {e}")
                continue
    
    return junction_db

def load_ensembl_annotations(species, annotation_file=None):
    """
    加载Ensembl注释数据库
    如果提供annotation_file，从文件加载；否则使用模拟数据
    """
    ens_junction_coord_db = {}

    if annotation_file and os.path.exists(annotation_file):
        print(f"从文件加载Ensembl注释: {annotation_file}")
        try:
            with open(annotation_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 假设格式: chr:start-end\tgene_id\texon_region_ids
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        coord_str = parts[0]
                        gene_id = parts[1]
                        exon_region_ids = parts[2]

                        # 解析坐标
                        try:
                            chr_coord = coord_str.split(':')
                            chr_name = chr_coord[0]
                            start_end = chr_coord[1].split('-')
                            start = int(start_end[0])
                            end = int(start_end[1])

                            key = (chr_name, start, end)
                            ens_junction_coord_db[key] = ExonAnnotationsSimple(gene_id, exon_region_ids)
                        except (ValueError, IndexError):
                            continue
        except Exception as e:
            print(f"加载注释文件失败: {e}")
            print("使用模拟数据")

    # 如果没有加载到数据，使用模拟数据
    if not ens_junction_coord_db:
        print("使用模拟Ensembl注释数据")
        example_annotations = [
            (('chrX', 100630759, 100629986), 'ENSG00000000003', 'E11.1-E13.1'),
            (('chr17', 37566506, 37566505), 'ENSG00000000003', 'I25.1'),
            (('chr1', 1000000, 2000000), 'ENSG00000000001', 'E1.1-E2.1'),
            (('chr2', 3000000, 4000000), 'ENSG00000000002', 'E5.1-E7.1'),
        ]

        for coord_key, gene_id, exon_region_ids in example_annotations:
            ens_junction_coord_db[coord_key] = ExonAnnotationsSimple(gene_id, exon_region_ids)

    print(f"加载了 {len(ens_junction_coord_db)} 个已知junction注释")
    return ens_junction_coord_db

def annotate_known_junctions(junction_db, ens_junction_coord_db):
    """
    注释已知junction - 完全按照AltAnalyze逻辑
    """
    novel_junction_db = {}
    known_count = 0
    
    for key in junction_db:
        ji = junction_db[key]
        if ji.BioType() == 'junction':
            if key in ens_junction_coord_db:
                # Known junction
                jd = ens_junction_coord_db[key]
                ji.setExonAnnotations(jd)
                # 按照AltAnalyze第1585行逻辑重新计算并设置UniqueID
                uid = jd.GeneID() + ':' + jd.ExonRegionIDs()
                ji.setUniqueID(uid)
                ji.setGeneID(jd.GeneID())
                known_count += 1
            else:
                # Novel junction
                novel_junction_db[key] = junction_db[key]
    
    print(f"找到 {known_count} 个已知junction，{len(novel_junction_db)} 个novel junction")
    return novel_junction_db

def annotate_novel_junctions(novel_junction_db, gene_mapping=None):
    """
    注释novel junction - 按照AltAnalyze的annotateNovelJunctions逻辑
    这里简化实现，实际需要复杂的exon mapping逻辑
    """
    processed_count = 0

    for key in novel_junction_db:
        chr_name, exon1_stop, exon2_start = key
        ji = novel_junction_db[key]

        # 尝试从基因映射中获取基因ID
        gene_id = None
        if gene_mapping:
            # 查找与junction坐标重叠的基因
            for gene_key, mapped_gene_id in gene_mapping.items():
                gene_chr, gene_start, gene_end = gene_key
                if (chr_name == gene_chr and
                    gene_start <= exon1_stop <= gene_end and
                    gene_start <= exon2_start <= gene_end):
                    gene_id = mapped_gene_id
                    break

        if gene_id is None:
            # 如果没有找到映射的基因，创建一个novel基因ID
            gene_id = f"NOVEL_GENE_{chr_name}_{min(exon1_stop, exon2_start)}"

        # 检测intron retention - 完全按照AltAnalyze逻辑
        if abs(exon2_start - exon1_stop) == 1:
            # Intron retention事件
            # 按照AltAnalyze的IntronRegionID格式: 'I'+str(exon_num)+'.1'
            region_id = f"I1.1_{min(exon1_stop, exon2_start)}"
            uid = f"{gene_id}:{region_id}"
            ji.setGeneID(gene_id)
            ji.setUniqueID(uid)
            processed_count += 1
        else:
            # 标准novel junction
            # 模拟AltAnalyze的region1和region2生成逻辑

            # 简化的splice site检测
            # 实际AltAnalyze会检查SpliceSitesFound()状态
            splice_sites_found = 'none'  # 简化假设

            if splice_sites_found == 'both':
                # 两个splice site都已知的情况
                region1 = f"E1.1"  # 简化
                region2 = f"E2.1"  # 简化
            else:
                # 部分或全部splice site未知的情况
                region1 = f"E1.1_{exon1_stop}"
                region2 = f"E2.1_{exon2_start}"

            # 检查是否为trans-splicing（跨基因）
            # 这里简化处理，实际需要复杂的基因重叠检测
            trans_splicing = 'no'

            if trans_splicing == 'yes':
                # Trans-splicing格式: GeneID1:region1-GeneID2:region2
                secondary_gene_id = f"NOVEL_GENE2_{chr_name}_{exon2_start}"
                uid = f"{gene_id}:{region1}-{secondary_gene_id}:{region2}"
                ji.setSecondaryGeneID(secondary_gene_id)
                ji.setTransSplicing('yes')
            else:
                # 标准novel junction格式: GeneID:region1-region2
                uid = f"{gene_id}:{region1}-{region2}"

            ji.setGeneID(gene_id)
            ji.setUniqueID(uid)
            ji.setSpliceSitesFound(splice_sites_found)
            processed_count += 1

    print(f"处理了 {processed_count} 个novel junction")

def process_bed_directory(bed_dir, species, annotation_file=None, gene_mapping_file=None):
    """
    处理bed目录中的所有bed文件
    """
    # 查找所有bed文件
    bed_files = []
    for pattern in ['*__junction.bed', '*__intronJunction.bed', '*.bed']:
        bed_files.extend(glob.glob(os.path.join(bed_dir, pattern)))

    if not bed_files:
        print(f"在目录 {bed_dir} 中未找到bed文件")
        return {}, {}

    print(f"找到 {len(bed_files)} 个bed文件:")
    for bed_file in bed_files:
        print(f"  - {os.path.basename(bed_file)}")

    # 合并所有bed文件的junction数据
    all_junction_db = {}
    file_junction_counts = {}

    for bed_file in bed_files:
        print(f"\n处理文件: {os.path.basename(bed_file)}")
        junction_db = parse_bed_file(bed_file)
        file_junction_counts[bed_file] = len(junction_db)

        # 合并到总的数据库中（如果有重复坐标，保留第一个）
        for key, ji in junction_db.items():
            if key not in all_junction_db:
                all_junction_db[key] = ji

    print(f"\n文件解析统计:")
    for bed_file, count in file_junction_counts.items():
        print(f"  {os.path.basename(bed_file)}: {count} junctions")
    print(f"总共解析了 {len(all_junction_db)} 个unique junction")

    # 加载基因映射（如果提供）
    gene_mapping = None
    if gene_mapping_file and os.path.exists(gene_mapping_file):
        gene_mapping = load_gene_mapping(gene_mapping_file)

    # 加载Ensembl注释
    ens_junction_coord_db = load_ensembl_annotations(species, annotation_file)

    # 注释已知junction
    novel_junction_db = annotate_known_junctions(all_junction_db, ens_junction_coord_db)

    # 注释novel junction
    annotate_novel_junctions(novel_junction_db, gene_mapping)

    # 最终过滤 - 按照AltAnalyze逻辑
    junction_simple_db = {}
    filtered_out_count = 0

    for key in all_junction_db:
        ji = all_junction_db[key]
        if ji.GeneID() is not None and ji.UniqueID() is not None:
            junction_simple_db[key] = ji.UniqueID()
        else:
            filtered_out_count += 1

    print(f"\n过滤统计:")
    print(f"  通过过滤的junction: {len(junction_simple_db)}")
    print(f"  被过滤掉的junction: {filtered_out_count}")

    return all_junction_db, junction_simple_db

def load_gene_mapping(gene_mapping_file):
    """
    加载基因映射文件
    格式: chr:start-end\tgene_id
    """
    gene_mapping = {}
    try:
        with open(gene_mapping_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                parts = line.split('\t')
                if len(parts) >= 2:
                    coord_str = parts[0]
                    gene_id = parts[1]

                    # 解析坐标
                    try:
                        chr_coord = coord_str.split(':')
                        chr_name = chr_coord[0]
                        start_end = chr_coord[1].split('-')
                        start = int(start_end[0])
                        end = int(start_end[1])

                        key = (chr_name, start, end)
                        gene_mapping[key] = gene_id
                    except (ValueError, IndexError):
                        continue

        print(f"加载了 {len(gene_mapping)} 个基因映射")
    except Exception as e:
        print(f"加载基因映射文件失败: {e}")

    return gene_mapping

def write_mapping_results(junction_db, junction_simple_db, output_file):
    """
    输出ID映射结果
    """
    passed_count = 0
    filtered_count = 0
    known_count = 0
    novel_count = 0
    intron_retention_count = 0

    with open(output_file, 'w') as f:
        # 写入表头
        f.write("Original_Junction_ID\tCoordinates\tAltAnalyze_ID\tAltAnalyze_ID_with_Coords\tGene_ID\tStrand\tJunction_Type\tEvent_Type\tStatus\tReads_Filter\n")

        for key in sorted(junction_db.keys()):
            ji = junction_db[key]
            chr_name, exon1_stop, exon2_start = key
            coordinates = f"{chr_name}:{exon1_stop}-{exon2_start}"

            # 确定状态
            if key in junction_simple_db:
                status = "PASSED_FILTER"
                altanalyze_id = ji.UniqueID()
                # 生成带坐标的完整ID（如counts.original.txt格式）
                altanalyze_id_with_coords = f"{altanalyze_id}={coordinates}"
                passed_count += 1
            else:
                status = "FILTERED_OUT"
                altanalyze_id = "N/A"
                altanalyze_id_with_coords = "N/A"
                filtered_count += 1

            # 确定junction类型
            if ji.ExonAnnotations() is not None:
                junction_type = "KNOWN"
                known_count += 1
            else:
                junction_type = "NOVEL"
                novel_count += 1

            # 确定事件类型
            event_type = "STANDARD"
            if altanalyze_id != "N/A":
                if ":I" in altanalyze_id:
                    event_type = "INTRON_RETENTION"
                    intron_retention_count += 1
                elif "-" in altanalyze_id and ":" in altanalyze_id.split("-")[1]:
                    event_type = "TRANS_SPLICING"
                elif "_" in altanalyze_id:
                    event_type = "ALTERNATIVE_SPLICE_SITE"

            gene_id = ji.GeneID() if ji.GeneID() is not None else "N/A"
            reads_filter = "PASS" if status == "PASSED_FILTER" else "FAIL"

            f.write(f"{ji.JunctionID()}\t{coordinates}\t{altanalyze_id}\t{altanalyze_id_with_coords}\t{gene_id}\t{ji.Strand()}\t{junction_type}\t{event_type}\t{status}\t{reads_filter}\n")

    # 输出统计信息
    print(f"\n映射结果统计:")
    print(f"  总junction数: {len(junction_db)}")
    print(f"  通过过滤: {passed_count}")
    print(f"  被过滤: {filtered_count}")
    print(f"  已知junction: {known_count}")
    print(f"  Novel junction: {novel_count}")
    print(f"  Intron retention事件: {intron_retention_count}")
    print(f"\n结果已写入: {output_file}")

def write_counts_format(junction_simple_db, output_file):
    """
    输出类似counts.original.txt格式的文件（仅包含ID和坐标）
    """
    counts_file = output_file.replace('.txt', '_counts_format.txt')

    with open(counts_file, 'w') as f:
        f.write("AltAnalyze_ID\n")

        for key in sorted(junction_simple_db.keys()):
            chr_name, exon1_stop, exon2_start = key
            coordinates = f"{chr_name}:{exon1_stop}-{exon2_start}"
            altanalyze_id = junction_simple_db[key]

            # 按照AltAnalyze counts.original.txt格式
            f.write(f"{altanalyze_id}={coordinates}\n")

    print(f"Counts格式文件已写入: {counts_file}")

def main():
    parser = argparse.ArgumentParser(
        description='BED文件到AltAnalyze ID映射工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法（使用模拟注释数据）
  python bed_to_altanalyze_id_mapper.py --bed_dir /path/to/bed/files

  # 使用自定义注释文件
  python bed_to_altanalyze_id_mapper.py --bed_dir /path/to/bed/files --annotation_file ensembl_junctions.txt

  # 指定输出文件
  python bed_to_altanalyze_id_mapper.py --bed_dir /path/to/bed/files --output my_mapping.txt

注释文件格式:
  chr:start-end<TAB>gene_id<TAB>exon_region_ids
  例如: chrX:100630759-100629986	ENSG00000000003	E11.1-E13.1
        """
    )

    parser.add_argument('--bed_dir', required=True,
                       help='包含bed文件的目录路径（支持*__junction.bed和*__intronJunction.bed）')
    parser.add_argument('--species', default='Hs',
                       help='物种代码 (默认: Hs)')
    parser.add_argument('--output', default='bed_to_altanalyze_mapping.txt',
                       help='输出映射文件路径 (默认: bed_to_altanalyze_mapping.txt)')
    parser.add_argument('--annotation_file',
                       help='Ensembl junction注释文件路径（可选，不提供则使用模拟数据）')
    parser.add_argument('--gene_mapping_file',
                       help='基因映射文件路径（可选，用于novel junction的基因分配）')
    parser.add_argument('--counts_format', action='store_true',
                       help='同时输出counts.original.txt格式的文件')

    args = parser.parse_args()

    # 验证输入
    if not os.path.exists(args.bed_dir):
        print(f"错误: 目录 {args.bed_dir} 不存在")
        sys.exit(1)

    if args.annotation_file and not os.path.exists(args.annotation_file):
        print(f"警告: 注释文件 {args.annotation_file} 不存在，将使用模拟数据")
        args.annotation_file = None

    if args.gene_mapping_file and not os.path.exists(args.gene_mapping_file):
        print(f"警告: 基因映射文件 {args.gene_mapping_file} 不存在")
        args.gene_mapping_file = None

    print("=" * 60)
    print("BED文件到AltAnalyze ID映射工具")
    print("=" * 60)
    print(f"输入目录: {args.bed_dir}")
    print(f"物种代码: {args.species}")
    print(f"输出文件: {args.output}")
    if args.annotation_file:
        print(f"注释文件: {args.annotation_file}")
    if args.gene_mapping_file:
        print(f"基因映射: {args.gene_mapping_file}")
    print("=" * 60)

    # 处理bed文件
    junction_db, junction_simple_db = process_bed_directory(
        args.bed_dir, args.species, args.annotation_file, args.gene_mapping_file
    )

    if not junction_db:
        print("错误: 未找到有效的junction数据")
        sys.exit(1)

    # 输出映射结果
    write_mapping_results(junction_db, junction_simple_db, args.output)

    # 如果需要，输出counts格式
    if args.counts_format:
        write_counts_format(junction_simple_db, args.output)

    print("\n" + "=" * 60)
    print("处理完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
