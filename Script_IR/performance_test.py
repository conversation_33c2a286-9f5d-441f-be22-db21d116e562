#!/usr/bin/env python3
"""
性能测试脚本 - 比较不同优化版本的性能
"""

import time
import psutil
import os
import subprocess
import sys

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

def run_script_with_monitoring(script_path, test_name):
    """运行脚本并监控性能"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"脚本: {script_path}")
    print(f"{'='*60}")
    
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # 运行脚本
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=3600)
        
        end_time = time.time()
        end_memory = get_memory_usage()
        
        execution_time = end_time - start_time
        memory_used = end_memory - start_memory
        
        print(f"执行时间: {execution_time:.2f} 秒")
        print(f"内存使用: {memory_used:.2f} MB")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 执行成功")
        else:
            print("❌ 执行失败")
            print("错误输出:")
            print(result.stderr)
        
        return {
            'name': test_name,
            'script': script_path,
            'time': execution_time,
            'memory': memory_used,
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except subprocess.TimeoutExpired:
        print("❌ 执行超时 (1小时)")
        return {
            'name': test_name,
            'script': script_path,
            'time': 3600,
            'memory': 0,
            'success': False,
            'stdout': '',
            'stderr': 'Timeout'
        }
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return {
            'name': test_name,
            'script': script_path,
            'time': 0,
            'memory': 0,
            'success': False,
            'stdout': '',
            'stderr': str(e)
        }

def main():
    """主函数"""
    print("开始性能测试...")
    print(f"系统信息:")
    print(f"  CPU核心数: {psutil.cpu_count()}")
    print(f"  总内存: {psutil.virtual_memory().total / 1024**3:.1f} GB")
    print(f"  可用内存: {psutil.virtual_memory().available / 1024**3:.1f} GB")
    
    # 测试脚本列表
    test_scripts = [
        ("05.IR_Pair_Find_2.py", "优化版本"),
        ("05.IR_Pair_Find_2_Ultra_Optimized.py", "超级优化版本")
    ]
    
    results = []
    
    for script_path, test_name in test_scripts:
        if os.path.exists(script_path):
            result = run_script_with_monitoring(script_path, test_name)
            results.append(result)
        else:
            print(f"⚠️  脚本不存在: {script_path}")
    
    # 输出总结
    print(f"\n{'='*80}")
    print("性能测试总结")
    print(f"{'='*80}")
    
    if results:
        print(f"{'测试名称':<20} {'执行时间(秒)':<15} {'内存使用(MB)':<15} {'状态':<10}")
        print("-" * 70)
        
        for result in results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            print(f"{result['name']:<20} {result['time']:<15.2f} {result['memory']:<15.2f} {status:<10}")
        
        # 找出最快的版本
        successful_results = [r for r in results if r['success']]
        if successful_results:
            fastest = min(successful_results, key=lambda x: x['time'])
            print(f"\n🏆 最快版本: {fastest['name']} ({fastest['time']:.2f}秒)")
            
            # 计算性能提升
            if len(successful_results) > 1:
                slowest = max(successful_results, key=lambda x: x['time'])
                improvement = (slowest['time'] - fastest['time']) / slowest['time'] * 100
                print(f"📈 性能提升: {improvement:.1f}%")
    
    print(f"\n测试完成！")

if __name__ == "__main__":
    main()
