#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Tumor-Specific.draw.py
@Time    :   2025/06/27 16:10:07
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import numpy as np
import anndata as ad
import os
from scipy.sparse import csr_matrix, csc_matrix  # 用于判断稀疏矩阵类型
from scipy import stats
from statsmodels.stats.multitest import multipletests
import pandas as pd
from matplotlib import pyplot as plt
import seaborn as sns
import os
from matplotlib.patches import Patch

def h5ad_to_dataframe(adata):
    #"""将anndata对象的X矩阵转换为DataFrame（包含行/列索引）"""
    # 1. 处理X矩阵（稀疏矩阵转密集数组）
    if isinstance(adata.X, (csr_matrix, csc_matrix)):
        X_array = adata.X.toarray()  # 稀疏矩阵转numpy数组
    else:
        X_array = adata.X  # 直接使用原数组（如已是numpy数组）
    # 2. 转换为DataFrame（行索引：样本名，列索引：特征名）
    df = pd.DataFrame(
        data=X_array,
        index=adata.obs.index,       # 行索引：样本ID（如TCGA样本名）
        columns=adata.var.index      # 列索引：特征名（如junction名称）
    )
    return df


def draw_heatmap(sample_type_data,data_plot,savepath):
    data_plot = data_plot.clip(lower=0, upper=30)
    subregion = dict()
    for i in sample_type_data.index:
        CancerType = sample_type_data.loc[i,"Group"]
        if CancerType == "Cancer":
            sample_type_data.loc[i,"Color"] = "red"
        if CancerType == "Normal":
            sample_type_data.loc[i,"Color"] = "blue"
        if CancerType == "Gtex":
            sample_type_data.loc[i,"Color"] = "green"
        if CancerType == "TCGA":
            sample_type_data.loc[i,"Color"] = "orange"
        if CancerType == "GtexSkin":
            sample_type_data.loc[i,"Color"] = "purple"
        subregion[CancerType] = sample_type_data.loc[i,"Color"]
    handles = [Patch(facecolor=subregion[name]) for name in subregion]
    #########################################################
    col_color = list(sample_type_data["Color"].values)
    ##########################################################
    ##########################################################
    # 注意：sns.clustermap会创建自己的figure，所以不需要plt.figure()
    g = sns.clustermap(data=data_plot, #data:数据集
                method='average', #method: 聚类方法，默认为“average”,可选’single’，’complete’ ,’weighted’,’centroid’,’median’.
                metric='euclidean', #metric：簇之间距离选择,默认为欧式距离euclidean
                row_cluster=True,  #row_cluster：行方向聚类
                col_cluster=False, #col_cluster：列方向聚类
                col_colors=col_color,# col_colors：列标记的颜色列表
                cmap="RdBu_r", #cmap:设置热图颜色带的色系
                xticklabels =False,
                yticklabels =False,
                figsize=(20, 10),  # 在这里设置图片尺寸 (宽度, 高度)
                #z_score=0,
                )
    plt.legend(handles,subregion,title='Cancer_Type',bbox_to_anchor=(1, 1), bbox_transform=plt.gcf().transFigure, loc='upper right')
    g.savefig(savepath, dpi=200, bbox_inches='tight', facecolor="white") # 保存图片；dpi：输出图像的分辨率；bbox_inches='tight'：去除空白；facecolor:背景颜色
    plt.show()#展示图像


pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/06.SampleSplice_IR_Anno_Merge_Optimized_Correct_Output-Top-dup-All.txt"
pair_info_data = pd.read_csv(pair_info_File,sep="\t",header=0,skiprows=0)
pair_info_data = pair_info_data[pair_info_data["intersect_intron"]>=0.95]
pair_info_data = pair_info_data[pair_info_data["intersect_exon"]<=0.05]
pair_info_data = pair_info_data[pair_info_data["intersect_pos1"]==1]
pair_info_data = pair_info_data[pair_info_data["intersect_pos2"]==1]
print(pair_info_data)
pair_info_data = pair_info_data[pair_info_data["P_value_x"]<0.01]
pair_info_data = pair_info_data[pair_info_data["P_value_y"]<0.01]
pair_info_data = pair_info_data[pair_info_data["Diff_Gtex_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_TCGA_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Skin_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Gtex_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_TCGA_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Skin_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Skin_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Gtex_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["TCGA_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Skin_SampleRatio_y"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Gtex_SampleRatio_y"]<0.001]
pair_info_data = pair_info_data[pair_info_data["TCGA_SampleRatio_y"]<0.001]

print(pair_info_data)
pair_info_data = pair_info_data.head(100)

db_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/02.database/SNAF_download/data"
purned_counts_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/ExpressionInput/counts.original.pruned.txt"
purned_counts_data = pd.read_csv(purned_counts_file,sep="\t",header=0,skiprows=0)
purned_counts_data = purned_counts_data.rename(columns={'Unnamed: 0': 'Alt_Splice'})
purned_counts_data = purned_counts_data[purned_counts_data["Alt_Splice"].isin(pair_info_data["Alt_Splice_x"]) | purned_counts_data["Alt_Splice"].isin(pair_info_data["Alt_Splice_y"])]
print(purned_counts_data)


gtex_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','GTEx_junction_counts.h5ad'))
gtex_ctrl_df = h5ad_to_dataframe(gtex_ctrl_db)
gtex_ctrl_df = gtex_ctrl_df.loc[gtex_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
gtex_ctrl_df.insert(0, 'Alt_Splice', gtex_ctrl_df.index)


tcga_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','tcga_matched_control_junction_count.h5ad'))
tcga_ctrl_df = h5ad_to_dataframe(tcga_ctrl_db)
tcga_ctrl_df = tcga_ctrl_df.loc[tcga_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
tcga_ctrl_df.insert(0, 'Alt_Splice', tcga_ctrl_df.index)


gtex_skin_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','gtex_skin_count.h5ad'))
gtex_skin_ctrl_df = h5ad_to_dataframe(gtex_skin_ctrl_db)
gtex_skin_ctrl_df = gtex_skin_ctrl_df.loc[gtex_skin_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
gtex_skin_ctrl_df.insert(0, 'Alt_Splice', gtex_skin_ctrl_df.index)


purned_counts_data = purned_counts_data.set_index("Alt_Splice")
gtex_ctrl_df = gtex_ctrl_df.set_index("Alt_Splice")
tcga_ctrl_df = tcga_ctrl_df.set_index("Alt_Splice")
gtex_skin_ctrl_df = gtex_skin_ctrl_df.set_index("Alt_Splice")

print(purned_counts_data)
print(gtex_ctrl_df)

cancer_samples_selected = purned_counts_data.columns.tolist()
gtex_samples_selected = gtex_ctrl_df.columns.tolist()
tcga_samples_selected = tcga_ctrl_df.columns.tolist()
skin_samples_selected = gtex_skin_ctrl_df.columns.tolist()


sample_groups = ['Cancer'] * len(cancer_samples_selected) \
                + ['Gtex'] * len(gtex_samples_selected) \
                + ['TCGA'] * len(tcga_samples_selected) \
                + ['GtexSkin'] * len(skin_samples_selected)

sample_info = pd.DataFrame({
        'Sample': cancer_samples_selected + gtex_samples_selected + tcga_samples_selected + skin_samples_selected,
        'Group': sample_groups
    })
print(sample_info)


combined_data = pd.merge(purned_counts_data,gtex_ctrl_df,left_index=True,right_index=True,how='outer')
combined_data = pd.merge(combined_data,tcga_ctrl_df,left_index=True,right_index=True,how='outer')
combined_data = pd.merge(combined_data,gtex_skin_ctrl_df,left_index=True,right_index=True,how='outer')
print(combined_data)
combined_data = combined_data.fillna(0)
print(combined_data)


save_path = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/07.Tumor-Specific.heatmap-sub.png"
draw_heatmap(sample_info,combined_data,save_path)



pair_info_data.to_csv("/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/07.Tumor-Specific.heatmap-sub.txt",sep="\t",header=True,index=False)
'''
pair_info_data = pair_info_data[pair_info_data["IntronReserved"]=="YES"]
print(pair_info_data)
#############################################################################
sample_splice_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/02.AltSplice_Statistic_Anno.txt"
sample_splice_data = pd.read_csv(sample_splice_file,sep="\t",header=0)
sample_splice_data = sample_splice_data[sample_splice_data["Alt_Splice"].isin(pair_info_data["Alt_Splice"])]
sample_splice_data = sample_splice_data[sample_splice_data["All_adjPvalue"]<0.001]
print(sample_splice_data)
#############################################################################
db_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/02.database/SNAF_download/data"
purned_counts_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/ExpressionInput/counts.original.pruned.txt"
purned_counts_data = pd.read_csv(purned_counts_file,sep="\t",header=0,skiprows=0)
purned_counts_data = purned_counts_data.rename(columns={'Unnamed: 0': 'Alt_Splice'})
purned_counts_data = purned_counts_data[purned_counts_data["Alt_Splice"].isin(sample_splice_data["Alt_Splice"])]
gtex_ctrl_db = ad.read_h5ad(os.path.join(db_dir,'controls','GTEx_junction_counts.h5ad'))
gtex_ctrl_df = h5ad_to_dataframe(gtex_ctrl_db)
gtex_ctrl_df = gtex_ctrl_df.loc[gtex_ctrl_df.index.isin(list(purned_counts_data["Alt_Splice"].values))]
gtex_ctrl_df.insert(0, 'Alt_Splice', gtex_ctrl_df.index)
print(purned_counts_data)
print(gtex_ctrl_df)
'''
