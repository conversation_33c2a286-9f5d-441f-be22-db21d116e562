import pandas as pd
import re
import string

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR.txt"
bed_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/bed"
ID_Data = pd.read_csv(IR_File,sep="\t",header=0,skiprows=0)
#ID_Data = ID_Data.sort_values(by="SampleID",ascending=False)
#ID_Data = ID_Data.head(100)

def process_bed_file(tdata):
    #print(tdata)
    chr, exon1_start, exon2_stop, junction_id, reads, strand = tdata[:6]
    lengths = tdata[10]
    # 解析lengths字段 - 完全按照AltAnalyze逻辑
    exon1_len, exon2_len = re.split(',',lengths)
    exon1_len = int(exon1_len)
    exon2_len = int(exon2_len)
    exon1_start = int(exon1_start)
    exon2_stop = int(exon2_stop)
    chr, exon1_stop, exon2_start = process_bed_coordinates(
                                    chr, exon1_start, exon2_stop, strand, 
                                    exon1_len, exon2_len
                                )
    return chr, exon1_stop, exon2_start

def process_bed_coordinates(chr, exon1_start, exon2_stop, strand, exon1_len, exon2_len):
    """
    处理BED文件坐标，完全按照AltAnalyze逻辑
    
    参数:
        chr: 染色体名称
        exon1_start: 第一个exon起始位置
        exon2_stop: 第二个exon结束位置  
        strand: 链方向 (+/-)
        exon1_len: 第一个exon长度
        exon2_len: 第二个exon长度
    
    返回:
        tuple: (chr, exon1_stop, exon2_start) - AltAnalyze使用的坐标键
    """
    
    # 染色体名称标准化 - 完全按照AltAnalyze逻辑
    if 'chr' not in chr:
        chr = 'chr' + chr
    if chr == 'chrM': 
        chr = 'chrMT'  # MT是Ensembl约定
    if chr == 'M': 
        chr = 'MT'
    
    # 坐标计算 - 完全按照AltAnalyze逻辑
    if strand == '-':  # 负链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
        
        # 负链关键步骤：交换exon顺序
        a = (exon1_start, exon1_stop)
        b = (exon2_start, exon2_stop)
        exon1_stop, exon1_start = b  # 将b的值赋给exon1
        exon2_stop, exon2_start = a  # 将a的值赋给exon2
        
    else:  # 正链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
    
    return chr, exon1_stop, exon2_start

ID_Data["Raw_ID"] = "-"
ID_Data["IntronInfo"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    gene_name = re.split(":",Alt_Splice)[0]
    pos_info = re.split(":|-",pos_Examined)
    SampleID = re.split("\.",SampleInfo)[0]
    chr_info = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__intronJunction.bed"
    SampleData = pd.read_csv(SampleFile,sep="\t",header=None)
    SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
    if abs(int(pos_1) - int(pos_2)) == 1:
        SampleData_1 = SampleData[SampleData[0]==chr_info]
        SampleData_1 = SampleData_1[SampleData_1["Pos"].isin([pos_1,pos_2])]
        if len(SampleData_1) > 1:
            SampleData_1 = SampleData_1[SampleData_1["Gene"]==gene_name]
    else:
        SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__junction.bed"
        SampleData = pd.read_csv(SampleFile,sep="\t",header=None,skiprows=1)
        SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
        SampleData_1 = SampleData[SampleData[0]==chr_info]
        SampleData_1 = SampleData[SampleData["Exon"]==pos_1]
        SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_2]
        if len(SampleData_1) == 0:
            SampleData_1 = SampleData[SampleData[0]==chr_info]
            SampleData_1 = SampleData[SampleData["Exon"]==pos_2]
            SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_1]
    SampleData_1 = SampleData_1[SampleData_1[4]==SupportReads_1]
    if len(SampleData_1) == 1:
        SampleData_1["IntronInfo"] = SampleData_1["Gene"] + ":" + SampleData_1["Exon"]
        SupportReads_2 = SampleData_1[4].values[0]
        raw_id = SampleData_1[3].values[0]
        raw_gene_exon = SampleData_1["IntronInfo"].values[0]
        #print("######################################")
        #print(SampleData_1)
        #print(f"{SampleID}\t{gene_name}\t{pos_info}\t{len(SampleData_1)}\t{SupportReads_1}\t{SupportReads_2}")
    else:
        raw_id = "-"
        raw_gene_exon = "-"
    ID_Data.loc[i,"Raw_ID"] = raw_id
    ID_Data.loc[i,"IntronInfo"] = raw_gene_exon
    '''
    SampleData_2 = SampleData_1.copy()
    if len(SampleData_1) < 1:
        print(SampleData)
        print(SampleData_2)
        print(f"{SampleID}\t{gene_name}\t{pos_info}")
    if len(SampleData_1) >= 2:
        print(ID_Data.loc[[i]])
        print(SampleData_1)
        print("######################################")
    '''
out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR_Anno.txt"
ID_Data.to_csv(out_File,header=True,index=False,sep="\t")
#print(ID_Data)