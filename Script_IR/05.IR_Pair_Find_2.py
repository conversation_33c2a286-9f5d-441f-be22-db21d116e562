import pandas as pd
import re
import string

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR.txt"
bed_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/bed"
ID_Data = pd.read_csv(IR_File,sep="\t",header=0,skiprows=0)
ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"],keep="first")
ID_Data = ID_Data.sort_values(by="SampleID",ascending=False)
#print(ID_Data)
#ID_Data = ID_Data.head(100)
ID_Data["Raw_ID"] = "-"
ID_Data["IntronInfo"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    gene_name = re.split(":",Alt_Splice)[0]
    pos_info = re.split(":|-",pos_Examined)
    SampleID = re.split("\.",SampleInfo)[0]
    chr_info = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__intronJunction.bed"
    SampleData = pd.read_csv(SampleFile,sep="\t",header=None)
    SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
    if abs(int(pos_1) - int(pos_2)) == 1:
        SampleData_1 = SampleData[SampleData[0]==chr_info]
        SampleData_1 = SampleData_1[SampleData_1["Pos"].isin([pos_1,pos_2])]
        if len(SampleData_1) > 1:
            SampleData_1 = SampleData_1[SampleData_1["Gene"]==gene_name]
    else:
        SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__junction.bed"
        SampleData = pd.read_csv(SampleFile,sep="\t",header=None,skiprows=1)
        SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
        SampleData_1 = SampleData[SampleData[0]==chr_info]
        SampleData_1 = SampleData[SampleData["Exon"]==pos_1]
        SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_2]
        if len(SampleData_1) == 0:
            SampleData_1 = SampleData[SampleData[0]==chr_info]
            SampleData_1 = SampleData[SampleData["Exon"]==pos_2]
            SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_1]
    SampleData_1 = SampleData_1[SampleData_1[4]==SupportReads_1]
    if len(SampleData_1) == 1:
        SampleData_1["IntronInfo"] = SampleData_1["Gene"] + ":" + SampleData_1["Exon"]
        SupportReads_2 = SampleData_1[4].values[0]
        raw_id = SampleData_1[3].values[0]
        raw_gene_exon = SampleData_1["IntronInfo"].values[0]
    else:
        raw_id = "-"
        raw_gene_exon = "-"
    ID_Data.loc[i,"Raw_ID"] = raw_id
    ID_Data.loc[i,"IntronInfo"] = raw_gene_exon
out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/03.SampleSplice_IR_Anno_Test2.txt"
ID_Data.to_csv(out_File,header=True,index=False,sep="\t")
#print(ID_Data)