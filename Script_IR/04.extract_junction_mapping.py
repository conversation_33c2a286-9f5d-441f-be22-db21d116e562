#!/usr/bin/env python
"""
Extract junction ID mapping from AltAnalyze intermediate files
Maps original BED junction IDs to annotated biological IDs
Includes AltAnalyze's complex splicing event classification logic
"""

import os
import sys
import re

def analyze_altanalyze_id_complexity(altanalyze_id):
    """
    Analyze AltAnalyze ID to determine splicing event type and complexity
    Based on AltAnalyze's actual naming conventions from RNASeq.py

    Key insight: The ID format is exactly: ji.uid = jd.GeneID()+':'+jd.ExonRegionIDs()
    Where jd.ExonRegionIDs() comes directly from Ensembl annotation database
    """
    if '=' not in altanalyze_id:
        return {
            'event_type': 'unknown',
            'complexity': 'simple',
            'genes': [],
            'exons': [],
            'coordinates': '',
            'special_features': []
        }

    # Split ID and coordinates
    id_part, coord_part = altanalyze_id.split('=', 1)

    # Initialize analysis result
    analysis = {
        'event_type': 'unknown',
        'complexity': 'simple',
        'genes': [],
        'exons': [],
        'coordinates': coord_part,
        'special_features': []
    }

    # Extract genes from ID
    if ':' in id_part:
        # Check for trans-splicing (multiple genes)
        if id_part.count('ENSG') > 1:
            # Trans-splicing event: GENE1:EXON-GENE2:EXON
            analysis['event_type'] = 'trans-splicing'
            analysis['complexity'] = 'complex'
            analysis['special_features'].append('cross_gene')

            # Extract both genes
            gene_parts = re.findall(r'(ENSG\d+)', id_part)
            analysis['genes'] = gene_parts

            # Extract exon information
            exon_parts = re.findall(r':(E\d+\.\d+(?:_\d+)?)', id_part)
            analysis['exons'] = exon_parts

        else:
            # Single gene event
            gene_match = re.search(r'(ENSG\d+)', id_part)
            if gene_match:
                analysis['genes'] = [gene_match.group(1)]

            # Extract exon/intron information after gene ID
            remaining_id = id_part.split(':', 1)[1] if ':' in id_part else id_part

            # Check for different event types
            if 'I' in remaining_id and 'E' in remaining_id:
                # Exon-Intron event: E13.1_coord-I25.1_coord
                analysis['event_type'] = 'intron-retention'
                analysis['complexity'] = 'complex'
                analysis['special_features'].append('exon_intron_junction')

                # Extract exon and intron IDs
                exon_intron_parts = re.findall(r'([EI]\d+\.\d+(?:_\d+)?)', remaining_id)
                analysis['exons'] = exon_intron_parts

            elif '_' in remaining_id:
                # Coordinate suffixes in AltAnalyze indicate specific splice site coordinates
                # These come from jd.ExonRegionIDs() in the Ensembl annotation database
                # Format: E11.1-E13.1_100629977 means exon region with specific coordinate

                # Count coordinate suffixes
                coord_suffixes = re.findall(r'_(\d+)', remaining_id)

                if len(coord_suffixes) == 1:
                    # Single coordinate suffix: E11.1-E13.1_100629977
                    # This indicates a specific splice site variant of the exon region
                    analysis['event_type'] = 'coordinate-specific-splicing'
                    analysis['complexity'] = 'moderate'
                    analysis['special_features'].append('coordinate_specific_splice_site')
                    analysis['special_features'].append('coordinate_suffix_' + coord_suffixes[0])

                elif len(coord_suffixes) > 1:
                    # Multiple coordinate suffixes: complex coordinate-specific splicing
                    analysis['event_type'] = 'complex-coordinate-splicing'
                    analysis['complexity'] = 'complex'
                    analysis['special_features'].append('multiple_coordinate_sites')
                    for suffix in coord_suffixes:
                        analysis['special_features'].append('coordinate_suffix_' + suffix)

                # Extract exon/intron IDs (including those with coordinate suffixes)
                exon_intron_parts = re.findall(r'([EI]\d+\.\d+(?:_\d+)?)', remaining_id)
                analysis['exons'] = exon_intron_parts

            else:
                # Standard exon skipping: E11.1-E13.1
                analysis['event_type'] = 'cassette-exon'
                analysis['complexity'] = 'simple'

                # Extract exon IDs
                exon_parts = re.findall(r'(E\d+\.\d+)', remaining_id)
                analysis['exons'] = exon_parts

                # Check if it's a simple skip or complex
                if len(exon_parts) == 2:
                    exon1_num = float(exon_parts[0].replace('E', '').replace('.', ''))
                    exon2_num = float(exon_parts[1].replace('E', '').replace('.', ''))
                    if abs(exon2_num - exon1_num) > 2:
                        analysis['special_features'].append('multi_exon_skip')
                        analysis['complexity'] = 'moderate'

    # Additional coordinate analysis
    if coord_part:
        # Check coordinate span
        if ':' in coord_part and '-' in coord_part:
            try:
                chr_part, pos_part = coord_part.split(':', 1)
                start_str, end_str = pos_part.split('-', 1)
                start_pos = int(start_str)
                end_pos = int(end_str)
                span = abs(end_pos - start_pos)

                if span > 100000:  # >100kb
                    analysis['special_features'].append('long_range_splicing')
                    analysis['complexity'] = 'complex'
                elif span > 10000:  # >10kb
                    analysis['special_features'].append('medium_range_splicing')
                    if analysis['complexity'] == 'simple':
                        analysis['complexity'] = 'moderate'

                # Check for negative strand (end < start)
                if end_pos < start_pos:
                    analysis['special_features'].append('negative_strand')

            except (ValueError, IndexError):
                analysis['special_features'].append('invalid_coordinates')

    return analysis

def classify_splicing_event(analysis):
    """
    Classify splicing event based on analysis results
    Returns a human-readable classification matching AltAnalyze's terminology
    """
    event_type = analysis['event_type']
    features = analysis['special_features']

    if event_type == 'trans-splicing':
        return 'Trans-splicing (Cross-gene fusion)'
    elif event_type == 'intron-retention':
        return 'Intron retention / Complex exon-intron junction'
    elif event_type == 'coordinate-specific-splicing':
        # Based on AltAnalyze's coordinate suffix logic from Ensembl annotations
        return 'Coordinate-specific splice site (Alternative boundary)'
    elif event_type == 'complex-coordinate-splicing':
        return 'Complex coordinate-specific splicing (Multiple boundaries)'
    elif event_type == 'cassette-exon':
        if 'multi_exon_skip' in features:
            return 'Multi-exon cassette (Complex skipping)'
        else:
            return 'Cassette exon (Standard exon skipping)'
    else:
        return 'Unknown/Unclassified splicing event'

def parse_bed_files(bed_dir):
    """Parse BED files to extract original junction IDs and coordinates"""
    junction_data = {}

    for filename in os.listdir(bed_dir):
        # Process all BED files (junction and intronJunction) using the same logic as AltAnalyze
        if filename.endswith('.bed') and ('junction' in filename.lower() or 'intron' in filename.lower()):
            filepath = os.path.join(bed_dir, filename)

            # Determine sample name and file type
            if '__junction.bed' in filename:
                sample_name = filename.replace('.Aligned.sortedByCoord.out__junction.bed', '')
                file_type = 'junction'
            elif '__intronJunction.bed' in filename:
                sample_name = filename.replace('.Aligned.sortedByCoord.out__intronJunction.bed', '')
                file_type = 'intronJunction'
            else:
                # Fallback for other naming patterns
                sample_name = filename.replace('.bed', '')
                file_type = 'unknown'

            print("Processing {}...".format(filename))

            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('track') or line.startswith('#'):
                        continue

                    parts = line.split('\t')
                    if len(parts) >= 12:
                        chr_name = parts[0]
                        exon1_start = int(parts[1])
                        exon2_stop = int(parts[2])
                        junction_id = parts[3]
                        reads = parts[4]
                        strand = parts[5]
                        lengths = parts[10]

                        # Use AltAnalyze's exact coordinate calculation logic (RNASeq.py lines 700-714)
                        exon1_len, exon2_len = map(int, lengths.split(',')[:2])

                        if strand == '-':
                            if (exon1_len + exon2_len) == 0:  # Kallisto-Splice directly reports coordinates
                                exon1_stop = exon1_start
                                exon2_start = exon2_stop
                            else:
                                exon1_stop = exon1_start + exon1_len
                                exon2_start = exon2_stop - exon2_len + 1
                            # Exons have opposite order for negative strand
                            a = (exon1_start, exon1_stop)
                            b = (exon2_start, exon2_stop)
                            exon1_stop, exon1_start = b
                            exon2_stop, exon2_start = a
                            final_coord = (chr_name, exon1_stop, exon2_start)
                        else:
                            if (exon1_len + exon2_len) == 0:  # Kallisto-Splice directly reports coordinates
                                exon1_stop = exon1_start
                                exon2_start = exon2_stop
                            else:
                                exon1_stop = exon1_start + exon1_len
                                exon2_start = exon2_stop - exon2_len + 1
                            final_coord = (chr_name, exon1_stop, exon2_start)

                        coord_str = "{}:{}-{}".format(final_coord[0], final_coord[1], final_coord[2])

                        # Handle multiple samples with same coordinates (aggregate data)
                        if final_coord in junction_data:
                            # Coordinate already exists, aggregate the data
                            existing_data = junction_data[final_coord]
                            existing_data['original_ids'].append(junction_id)
                            existing_data['reads_list'].append(reads)
                            existing_data['samples'].append(sample_name)
                            existing_data['file_types'].append(file_type)
                        else:
                            # New coordinate, create new entry
                            junction_data[final_coord] = {
                                'original_ids': [junction_id],
                                'coordinates': coord_str,
                                'reads_list': [reads],
                                'strand': strand,
                                'samples': [sample_name],
                                'file_types': [file_type]
                            }

    return junction_data

def parse_ensembl_annotations(annotation_file):
    """Parse Ensembl junction annotation file"""
    ensembl_annotations = {}

    if not os.path.exists(annotation_file):
        print("Ensembl annotation file not found: {}".format(annotation_file))
        return ensembl_annotations

    print("Processing Ensembl annotations: {}...".format(annotation_file))

    with open(annotation_file, 'r') as f:
        f.readline()  # Skip header

        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 10:
                gene = parts[0]
                exon_id = parts[1]
                chr_name = parts[2]
                strand = parts[3]
                start_coords = parts[4]  # format: start1|start2
                stop_coords = parts[5]   # format: stop1|stop2
                constitutive_call = parts[6]
                ens_exon_ids = parts[7]
                splice_events = parts[8]
                splice_junctions = parts[9]

                # Parse junction coordinates
                if '|' in start_coords and '|' in stop_coords:
                    start_parts = start_coords.split('|')
                    stop_parts = stop_coords.split('|')

                    try:
                        # For junction coordinates, use the junction boundaries
                        exon1_stop = int(start_parts[1]) if len(start_parts) > 1 else int(start_parts[0])
                        exon2_start = int(stop_parts[0]) if len(stop_parts) > 0 else int(stop_parts[1])

                        # Handle negative strand coordinate adjustment
                        if strand == '-':
                            coord_key = (chr_name, exon1_stop, exon2_start)
                        else:
                            coord_key = (chr_name, exon1_stop, exon2_start)

                        ensembl_annotations[coord_key] = {
                            'gene': gene,
                            'exon_id': exon_id,
                            'strand': strand,
                            'constitutive_call': constitutive_call,
                            'ens_exon_ids': ens_exon_ids,
                            'splice_events': splice_events,
                            'splice_junctions': splice_junctions,
                            'annotated_id': gene + ':' + exon_id
                        }
                    except ValueError:
                        continue

    return ensembl_annotations

def parse_counts_file(counts_file):
    """Parse counts file to extract annotated IDs"""
    annotated_data = {}

    if not os.path.exists(counts_file):
        print("Counts file not found: {}".format(counts_file))
        return annotated_data

    print("Processing {}...".format(counts_file))

    with open(counts_file, 'r') as f:
        header = f.readline().strip().split('\t')
        samples = header[1:]  # Skip AltAnalyze_ID column

        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                altanalyze_id = parts[0]

                # Parse AltAnalyze ID format: ID=coordinates
                if '=' in altanalyze_id:
                    annotated_id, coord_part = altanalyze_id.split('=', 1)

                    # Parse coordinates: chr:start-stop
                    if ':' in coord_part and '-' in coord_part:
                        chr_part, pos_part = coord_part.split(':', 1)
                        start_str, stop_str = pos_part.split('-', 1)

                        try:
                            start_pos = int(start_str)
                            stop_pos = int(stop_str)
                            coord_key = (chr_part, start_pos, stop_pos)

                            # Store reads counts for each sample
                            reads_data = {}
                            for i, sample in enumerate(samples):
                                if i + 1 < len(parts):
                                    reads_data[sample] = parts[i + 1]

                            annotated_data[coord_key] = {
                                'annotated_id': annotated_id,
                                'coordinates': coord_part,
                                'reads_data': reads_data
                            }
                        except ValueError:
                            continue

    return annotated_data

def create_mapping_file(junction_data, annotated_data, ensembl_annotations, output_file):
    """Create two mapping files:
    1. Aggregated version: one line per coordinate with integrated information
    2. Detailed version: one line per Original_Junction_ID without integration
    Only output junctions that have intersection with counts.original.txt"""

    print("Creating mapping files: {}...".format(output_file))
    print("Only including junctions found in counts.original.txt...")

    # Generate file names
    base_name = output_file.replace('.txt', '')
    aggregated_file = base_name + '_aggregated.txt'
    detailed_file = base_name + '_detailed.txt'

    print("Aggregated file: {}".format(aggregated_file))
    print("Detailed file: {}".format(detailed_file))

    # Create aggregated file (one line per coordinate)
    create_aggregated_file(junction_data, annotated_data, ensembl_annotations, aggregated_file)

    # Create detailed file (one line per Original_Junction_ID)
    create_detailed_file(junction_data, annotated_data, ensembl_annotations, detailed_file)

def create_aggregated_file(junction_data, annotated_data, ensembl_annotations, output_file):
    """Create aggregated mapping file - one line per coordinate"""

    with open(output_file, 'w') as f:
        # Write header without complexity analysis columns
        header_fields = [
            'Original_Junction_IDs',
            'Coordinates',
            'Annotated_ID',
            'Gene_ID',
            'Exon_ID',
            'Splice_Event',
            'Coordinate_Span_bp',
            'All_Samples_Reads',
            'Strand',
            'Samples_Found',
            'File_Types',
            'Status',
            'Source'
        ]
        f.write('\t'.join(header_fields) + '\n')

        # Process each unique coordinate that exists in counts file
        processed_count = 0

        for coord_key in junction_data:
            # Skip if this junction is not in the counts file
            if coord_key not in annotated_data:
                continue

            junction_info = junction_data[coord_key]

            # Initialize default values
            annotated_id = junction_info['coordinates']
            gene_id = 'Unknown'
            exon_id = 'Unknown'
            splice_event = 'Unknown'
            status = 'Novel'
            source = 'BED_only'

            # Initialize coordinate span
            coordinate_span = 'Unknown'

            # Calculate coordinate span
            try:
                coord_str = junction_info['coordinates']
                if ':' in coord_str and '-' in coord_str:
                    pos_part = coord_str.split(':', 1)[1]
                    start_str, end_str = pos_part.split('-', 1)
                    start_pos = int(start_str)
                    end_pos = int(end_str)
                    coordinate_span = str(abs(end_pos - start_pos))
                else:
                    coordinate_span = 'Unknown'
            except (ValueError, IndexError):
                coordinate_span = 'Invalid'

            # Since we only process junctions in annotated_data, this should always be true
            annot_info = annotated_data[coord_key]
            annotated_id = annot_info['annotated_id']
            status = 'Processed'
            source = 'AltAnalyze'

            # Calculate coordinate span for analysis
            try:
                coord_parts = junction_info['coordinates'].split(':')[1].split('-')
                start_pos = int(coord_parts[0])
                end_pos = int(coord_parts[1])
                coordinate_span = str(abs(end_pos - start_pos))
            except:
                coordinate_span = 'Unknown'

            # Check if also found in Ensembl annotations for additional details
            if coord_key in ensembl_annotations:
                ensembl_info = ensembl_annotations[coord_key]
                gene_id = ensembl_info['gene']
                exon_id = ensembl_info['exon_id']
                splice_event = ensembl_info['splice_events']
                status = 'Known'
                source = 'Ensembl+AltAnalyze'
            else:
                # In counts but not in Ensembl - extract info from annotated_id
                if ':' in annotated_id and annotated_id != junction_info['coordinates']:
                    gene_id = annotated_id.split(':')[0]
                    exon_id = annotated_id.split(':')[1] if ':' in annotated_id else 'Unknown'
                    status = 'Annotated'
                    source = 'AltAnalyze_annotated'

            # Write mapping line without complexity analysis fields
            line = '\t'.join([
                '|'.join(set(junction_info['original_ids'])),  # Unique original IDs from all samples
                junction_info['coordinates'],
                annotated_id,
                gene_id,
                exon_id,
                splice_event,
                coordinate_span,
                '|'.join(junction_info['reads_list']),  # All reads from all samples
                junction_info['strand'],
                '|'.join(junction_info['samples']),  # All samples that have this junction
                '|'.join(set(junction_info['file_types'])),  # Unique file types
                status,
                source
            ]) + '\n'

            f.write(line)
            processed_count += 1

        print("Processed {} unique junctions found in counts.original.txt".format(processed_count))

def create_detailed_file(junction_data, annotated_data, ensembl_annotations, output_file):
    """Create detailed mapping file - one line per Original_Junction_ID"""

    with open(output_file, 'w') as f:
        # Write header without complexity analysis columns
        header_fields = [
            'Original_Junction_ID',
            'Coordinates',
            'Annotated_ID',
            'Gene_ID',
            'Exon_ID',
            'Splice_Event',
            'Coordinate_Span_bp',
            'Reads',
            'Strand',
            'Sample',
            'File_Type',
            'Status',
            'Source'
        ]
        f.write('\t'.join(header_fields) + '\n')

        # Process each junction entry individually
        processed_count = 0

        for coord_key in junction_data:
            # Skip if this junction is not in the counts file
            if coord_key not in annotated_data:
                continue

            junction_info = junction_data[coord_key]

            # Get annotation information
            annot_info = annotated_data[coord_key]
            annotated_id = annot_info['annotated_id']
            status = 'Processed'
            source = 'AltAnalyze'

            # Calculate coordinate span
            try:
                coord_parts = junction_info['coordinates'].split(':')[1].split('-')
                start_pos = int(coord_parts[0])
                end_pos = int(coord_parts[1])
                coordinate_span = str(abs(end_pos - start_pos))
            except:
                coordinate_span = 'Unknown'

            # Initialize default values
            gene_id = 'Unknown'
            exon_id = 'Unknown'
            splice_event = 'Unknown'

            # Check if also found in Ensembl annotations for additional details
            if coord_key in ensembl_annotations:
                ensembl_info = ensembl_annotations[coord_key]
                gene_id = ensembl_info['gene']
                exon_id = ensembl_info['exon_id']
                splice_event = ensembl_info['splice_events']
                status = 'Known'
                source = 'Ensembl+AltAnalyze'
            else:
                # In counts but not in Ensembl - extract info from annotated_id
                if ':' in annotated_id and annotated_id != junction_info['coordinates']:
                    gene_id = annotated_id.split(':')[0]
                    exon_id = annotated_id.split(':')[1] if ':' in annotated_id else 'Unknown'
                    status = 'Annotated'
                    source = 'AltAnalyze_annotated'

            # Write one line for each original junction ID
            for i, original_id in enumerate(junction_info['original_ids']):
                line = '\t'.join([
                    original_id,
                    junction_info['coordinates'],
                    annotated_id,
                    gene_id,
                    exon_id,
                    splice_event,
                    coordinate_span,
                    junction_info['reads_list'][i],
                    junction_info['strand'],
                    junction_info['samples'][i],
                    junction_info['file_types'][i],
                    status,
                    source
                ]) + '\n'

                f.write(line)
                processed_count += 1

        print("Processed {} individual junction entries found in counts.original.txt".format(processed_count))

def main():
    if len(sys.argv) not in [4, 5]:
        print("Usage: python extract_junction_mapping.py <bed_dir> <counts_file> <output_file> [ensembl_annotation_file]")
        print("Example: python extract_junction_mapping.py /mnt/bed /mnt/altanalyze_output/ExpressionInput/counts.original.txt junction_mapping.txt")
        print("With Ensembl: python extract_junction_mapping.py /mnt/bed /mnt/altanalyze_output/ExpressionInput/counts.original.txt junction_mapping.txt /path/to/Hs_Ensembl_junction.txt")
        sys.exit(1)

    bed_dir = sys.argv[1]
    counts_file = sys.argv[2]
    output_file = sys.argv[3]
    ensembl_file = sys.argv[4] if len(sys.argv) == 5 else None

    print("=== Junction ID Mapping Extraction ===")
    print("BED directory: {}".format(bed_dir))
    print("Counts file: {}".format(counts_file))
    print("Output file: {}".format(output_file))
    if ensembl_file:
        print("Ensembl annotation file: {}".format(ensembl_file))
    print()

    # Step 1: Parse BED files
    print("Step 1: Parsing BED files...")
    junction_data = parse_bed_files(bed_dir)
    print("Found {} unique junctions".format(len(junction_data)))
    print()

    # Step 2: Parse counts file
    print("Step 2: Parsing counts file...")
    annotated_data = parse_counts_file(counts_file)
    print("Found {} annotated junctions".format(len(annotated_data)))
    print()

    # Step 3: Parse Ensembl annotations (if provided)
    ensembl_annotations = {}
    if ensembl_file:
        print("Step 3: Parsing Ensembl annotations...")
        ensembl_annotations = parse_ensembl_annotations(ensembl_file)
        print("Found {} Ensembl annotations".format(len(ensembl_annotations)))
        print()

    # Step 4: Create mapping file
    print("Step {}: Creating mapping file...".format(4 if ensembl_file else 3))
    create_mapping_file(junction_data, annotated_data, ensembl_annotations, output_file)
    print("Done!")
    print()

    # Summary - only count intersections with counts file
    intersection_count = sum(1 for coord in junction_data if coord in annotated_data)
    ensembl_intersection_count = sum(1 for coord in junction_data if coord in annotated_data and coord in ensembl_annotations)

    # Count by file type (check if any sample has junction/intronJunction)
    junction_file_count = 0
    intron_junction_file_count = 0
    total_samples_processed = 0

    for coord in junction_data:
        if coord in annotated_data:
            file_types = junction_data[coord]['file_types']
            total_samples_processed += len(junction_data[coord]['samples'])
            if 'junction' in file_types:
                junction_file_count += 1
            if 'intronJunction' in file_types:
                intron_junction_file_count += 1

    print("=== Summary ===")
    print("Total unique junctions in BED files: {}".format(len(junction_data)))
    print("Total sample-junction entries processed: {}".format(total_samples_processed))
    print("Unique junctions found in counts.original.txt: {}".format(intersection_count))
    print("  - Junctions with __junction.bed data: {}".format(junction_file_count))
    print("  - Junctions with __intronJunction.bed data: {}".format(intron_junction_file_count))
    if ensembl_file:
        print("Also found in Ensembl annotations: {}".format(ensembl_intersection_count))

    # Generate output file names
    base_name = output_file.replace('.txt', '')
    aggregated_file = base_name + '_aggregated.txt'
    detailed_file = base_name + '_detailed.txt'

    print("Mapping files created:")
    print("  - Aggregated (one line per coordinate): {}".format(aggregated_file))
    print("  - Detailed (one line per junction ID): {}".format(detailed_file))
    print("Note: Only junctions with intersection in counts.original.txt are included in output")

if __name__ == "__main__":
    main()
