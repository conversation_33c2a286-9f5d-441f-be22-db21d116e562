#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Tumor-Specific.draw.py
@Time    :   2025/06/27 16:10:07
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import numpy as np
import os


pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/06.SampleSplice_IR_Anno_Merge_Optimized_Correct_Output-Top-dup-All.txt"
pair_info_data = pd.read_csv(pair_info_File,sep="\t",header=0,skiprows=0)
pair_info_data = pair_info_data[pair_info_data["intersect_intron"]>=0.95]
pair_info_data = pair_info_data[pair_info_data["intersect_exon"]<=0.05]
pair_info_data = pair_info_data[pair_info_data["intersect_pos1"]==1]
pair_info_data = pair_info_data[pair_info_data["intersect_pos2"]==1]

print(pair_info_data)

pair_info_data = pair_info_data[pair_info_data["P_value_x"]<0.01]
pair_info_data = pair_info_data[pair_info_data["P_value_y"]<0.01]
pair_info_data = pair_info_data[pair_info_data["Diff_Gtex_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_TCGA_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Skin_x"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Gtex_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_TCGA_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Diff_Skin_y"]>0]
pair_info_data = pair_info_data[pair_info_data["Skin_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Gtex_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["TCGA_SampleRatio_x"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Skin_SampleRatio_y"]<0.001]
pair_info_data = pair_info_data[pair_info_data["Gtex_SampleRatio_y"]<0.001]
pair_info_data = pair_info_data[pair_info_data["TCGA_SampleRatio_y"]<0.001]

