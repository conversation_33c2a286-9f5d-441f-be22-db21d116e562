#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.IR_Diff_Top.py
@Time    :   2025/06/30 13:10:39
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
AnnoMergeFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/06.SampleSplice_IR_Anno_Merge_Optimized_Correct_Output.txt"
AnnoMergeData = pd.read_csv(AnnoMergeFile,sep="\t",header=0)
AS_Pair = AnnoMergeData[["IntronInfo","Alt_Splice_x","Alt_Splice_y","strand_x","strand_y","pos_Examined_x","pos_Examined_y"]]
AS_Pair = AS_Pair.drop_duplicates()
AS_Pair["pos_IGV"] = "-"
for i in AS_Pair.index:
    Alt_Splice_x = AS_Pair.loc[i,"Alt_Splice_x"]
    Alt_Splice_y = AS_Pair.loc[i,"Alt_Splice_y"]
    strand_x = AS_Pair.loc[i,"strand_x"]
    strand_y = AS_Pair.loc[i,"strand_y"]
    IntronInfo = AS_Pair.loc[i,"IntronInfo"]
    pos_Examined_x = AS_Pair.loc[i,"pos_Examined_x"]
    pos_Examined_y = AS_Pair.loc[i,"pos_Examined_y"]
    if strand_x != strand_y:
        print(f"链方向不一致：{Alt_Splice_x}和{Alt_Splice_y}")
    else:
        if IntronInfo.find("ENSG") >= 0:
            pos_info_1 = re.split(":|-",pos_Examined_x)
            pos_info_2 = re.split(":|-",pos_Examined_y)
            if strand_x == "+":
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[1]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[1]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
            else:
                chr_1 = pos_info_1[0]
                pos_1 = pos_info_1[2]
                chr_2 = pos_info_2[0]
                pos_2 = pos_info_2[2]
                if int(pos_1) > int(pos_2):
                    pos = f"{chr_1}:{pos_2}-{pos_1}"
                else:
                    pos = f"{chr_1}:{pos_1}-{pos_2}"
        else:
            pos_info = re.split(":|-",pos_Examined_x)
            chr = pos_info[0]
            pos_1 = pos_info[1]
            pos_2 = pos_info[2]
            if int(pos_1) > int(pos_2):
                pos = f"{chr}:{pos_2}-{pos_1}"
            else:
                pos = f"{chr}:{pos_1}-{pos_2}"
        AS_Pair.loc[i,"pos_IGV"] = pos
AS_Pair[['chr', 'start', 'end']] = AS_Pair['pos_IGV'].str.split(':|-', expand=True)
#AS_Pair = AS_Pair[AS_Pair["chr"]=="chr10"]

outfile1 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.txt"
outfile2 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed"
AS_Pair.to_csv(outfile1,sep="\t",header=True,index=False)
AS_Pair[['chr', 'start', 'end', 'pos_IGV']].to_csv(outfile2,sep="\t",header=False,index=False)



'''
awk 'BEGIN {FS=OFS="\t"} { $1="chr"$1; print }' /ml/dingyu/00.Software/AltAnalyze/usr/src/app/altanalyze/AltDatabase/EnsMart91/ensembl/Hs/Hs.bed > 07.Hs_chr_exon.bed
bedtools intersect -a 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b 07.Hs_chr.bed -wo> 07.SampleSplice_IR_Anno_Merge_Optimized_IGV_intersect.bed


'''