awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$2=$2-1; $3=$2+1} 1' 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.bed
awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$2=$3; $3=$3+1} 1' 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.bed

bedtools intersect -wao -a 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File/exon.sort.merge.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.intersect.bed
bedtools intersect -wao -a 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File/exon.sort.merge.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.intersect.bed


bedtools intersect -wao -a 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/intron.sort.merge.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.intron.intersect.bed
bedtools intersect -wao -a 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.sort.merge.bed > 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.exon.intersect.bed



######Update
awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$2=$2-1; $3=$2+2} 1' 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed > ./Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.bed
awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$2=$3-1; $3=$3+1} 1' 07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed > ./Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.bed

bedtools intersect -wao -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.rightpos.bed > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos1.intersect.bed
bedtools intersect -wao -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.leftpos.bed > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.pos2.intersect.bed

bedtools intersect -wao -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/intron.bed > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.intron.intersect.bed
bedtools intersect -wao -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.bed > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.exon.intersect.bed
bedtools intersect -wao -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.bed -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.sort.merge.bed > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/Update/07.SampleSplice_IR_Anno_Merge_Optimized_IGV.exonmerge.intersect.bed


