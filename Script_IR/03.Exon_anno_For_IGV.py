#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   03.Exon_anno_For_IGV.py
@Time    :   2025/06/23 10:40:06
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
anno_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/02.database/snaf_control_database/Alt91_db/Hs_Ensembl_exon_add_col.txt"
anno_data = pd.read_csv(anno_file,sep="\t",header=0,skiprows=0)
anno_data["name"] = anno_data["gene"] + ":" + anno_data["exon-id"] + ":" + anno_data["strand"]
anno_data = anno_data[["chromosome", "exon-region-start(s)","exon-region-stop(s)","name"]]
print(anno_data)

anno_out = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/04.Hs_Ensembl_exon_anno.bed"
anno_data.to_csv(anno_out,sep="\t",header=False,index=False)
