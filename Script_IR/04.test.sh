#!/bin/bash

# Junction ID Mapping Extraction Script
# This script extracts the mapping between original BED junction IDs and annotated biological IDs

echo "=== Junction ID Mapping Extraction ==="
echo "This script will create a mapping file showing the relationship between:"
echo "1. Original junction IDs from BED files (e.g., JUNC44329:100629986-100630759)"
echo "2. Annotated biological IDs (e.g., ENSG00000000003:E11.1-E13.1)"
echo "3. Processes both __junction.bed and __intronJunction.bed files"
echo "4. Only outputs junctions found in counts.original.txt"
echo ""

# Set paths
BED_DIR="/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/bed"
COUNTS_FILE="/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/ExpressionInput/counts.original.txt"
OUTPUT_FILE="/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult/04.junction_mapping_complete.txt"
ENSEMBL_FILE="/ml/dingyu/00.Software/AltAnalyze/usr/src/app/altanalyze/AltDatabase/EnsMart91/ensembl/Hs/Hs_Ensembl_junction.txt"

echo "Configuration:"
echo "BED directory: $BED_DIR"
echo "Counts file: $COUNTS_FILE"
echo "Ensembl annotation file: $ENSEMBL_FILE"
echo "Output file: $OUTPUT_FILE"
echo ""

# Check if files exist
if [ ! -d "$BED_DIR" ]; then
    echo "Error: BED directory not found: $BED_DIR"
    exit 1
fi

if [ ! -f "$COUNTS_FILE" ]; then
    echo "Error: Counts file not found: $COUNTS_FILE"
    exit 1
fi

if [ ! -f "$ENSEMBL_FILE" ]; then
    echo "Warning: Ensembl annotation file not found: $ENSEMBL_FILE"
    echo "Running without Ensembl annotations..."
    python 04.extract_junction_mapping.py "$BED_DIR" "$COUNTS_FILE" "$OUTPUT_FILE"
else
    echo "Running with complete annotations..."
    python 04.extract_junction_mapping.py "$BED_DIR" "$COUNTS_FILE" "$OUTPUT_FILE" "$ENSEMBL_FILE"
fi

echo ""
echo "=== Results ==="
if [ -f "$OUTPUT_FILE" ]; then
    echo "Mapping file created successfully: $OUTPUT_FILE"
    echo ""
    echo "File structure:"
    head -1 "$OUTPUT_FILE"
    echo ""
    echo "Sample entries:"
    head -5 "$OUTPUT_FILE" | tail -4
    echo ""
    echo "Total entries: $(wc -l < "$OUTPUT_FILE")"
    echo ""
    echo "You can now examine the mapping file to see:"
    echo "- Original_Junction_ID: The ID from your BED files"
    echo "- Coordinates: Genomic coordinates"
    echo "- Annotated_ID: The biological annotation (gene:exon)"
    echo "- Gene_ID: Ensembl gene ID"
    echo "- Exon_ID: Exon region ID"
    echo "- Splice_Event: Type of splicing event"
    echo "- Event_Classification: AltAnalyze event classification"
    echo "- Complexity_Level: Simple/Moderate/Complex"
    echo "- Special_Features: Additional features detected"
    echo "- Reads: Number of supporting reads"
    echo "- File_Type: junction or intronJunction"
    echo "- Status: Known/Novel/Processed"
    echo "- Source: Data source (Ensembl/AltAnalyze)"
    echo ""
    echo "Note: Only junctions found in counts.original.txt are included"
else
    echo "Error: Failed to create mapping file"
    exit 1
fi
