#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   run2.py
@Time    :   2025/07/09 14:34:17
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
exon_info = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/02.GTF_Bed_File_Process/exon.bed"
exon_5 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/02.GTF_Bed_File_Process/exon-5-start.bed"
exon_3 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/02.GTF_Bed_File_Process/exon-3-start.bed"

exon_data = pd.read_csv(exon_info,sep="\t",header=None)
for i in exon_data.index:
    chr = exon_data.loc[i,0]
    start = exon_data.loc[i,1]
    end = exon_data.loc[i,2]
    trans = exon_data.loc[i,3]
    strand = exon_data.loc[i,4]
    if strand == "+":
        chr = chr
        start_5 = start
        end_5 = start + 1
        start_3 = end - 1
        end_3 = end
    if strand == "-":
        chr = chr
        start_5 = end - 1
        end_5 = end
        start_3 = start
        end_3 = start + 1
    with open(exon_5,'a') as f5:
        f5.write(f"{chr}\t{start_5}\t{end_5}\t{trans}\t{strand}\n")
    with open(exon_3,'a') as f3:
        f3.write(f"{chr}\t{start_3}\t{end_3}\t{trans}\t{strand}\n")
print(exon_data)
