#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   01.filter_bam.py
@Time    :   2025/07/14 14:52:50
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import os
analysis_Dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer"
outfile = "01.filter_bam.sh"
unit_file = f"{analysis_Dir}/units.tsv"
unit_data = pd.read_csv(unit_file, sep="\t",header=0,skiprows=0)
#unit_data = unit_data.head(35)
num = 1
with open(outfile,"w") as ff:
    for i in unit_data.index:
        SampleID = unit_data.loc[i, "SampleID"]
        bam_file = f"{analysis_Dir}/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
        output_bam = f"{analysis_Dir}/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.filter.bam"
        cmd = f"samtools view -b -q 20 {bam_file} -@ 10 > {output_bam} &"
        #cmd = f"samtools index -@ 10 {output_bam}"
        if os.path.exists(bam_file):
            #print(f"{output_bam} exists")
            ff.write(f"{cmd}\n")
            if num % 10 == 0:
                ff.write(f"wait\n")
            num = num + 1
