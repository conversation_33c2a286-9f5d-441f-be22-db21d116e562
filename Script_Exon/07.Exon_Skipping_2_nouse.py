#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Exon_Skipping_2.py
@Time    :   2025/07/08 17:40:44
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon"
leftpos_intersect_file = f"{analysis_dir}/07.Exon_Skipping_leftpos_intersect.txt"
rightpos_intersect_file = f"{analysis_dir}/07.Exon_Skipping_rightpos_intersect.txt"
inner_exon_intersect_file = f"{analysis_dir}/07.Exon_Skipping_inner_exon_intersect.txt"
inner_intron_intersect_file = f"{analysis_dir}/07.Exon_Skipping_inner_intron_intersect.txt"

leftpos_intersect_data = pd.read_csv(leftpos_intersect_file, sep="\t", header=None, skiprows=0)
rightpos_intersect_data = pd.read_csv(rightpos_intersect_file, sep="\t", header=None, skiprows=0)
inner_exon_intersect_data = pd.read_csv(inner_exon_intersect_file, sep="\t", header=None, skiprows=0)
inner_intron_intersect_data = pd.read_csv(inner_intron_intersect_file, sep="\t", header=None, skiprows=0)
########################################
########################################

outdataframe = leftpos_intersect_data[[0,1,2,3]]
outdataframe = outdataframe.drop_duplicates()

########################################
########################################
leftpos_intersect_data["gene_Examined"] = leftpos_intersect_data[3].str.split(":",expand=True)[0]
leftpos_intersect_data["gene_anno"] = leftpos_intersect_data[7].str.split(":",expand=True)[0]
leftpos_intersect_data_samegene = leftpos_intersect_data[leftpos_intersect_data["gene_Examined"]==leftpos_intersect_data["gene_anno"]]
leftpos_intersect_data_samegene = leftpos_intersect_data_samegene.drop_duplicates(subset=[0,1,2,3],keep="first")
leftpos_intersect_data_diffgene = leftpos_intersect_data[leftpos_intersect_data["gene_Examined"]!=leftpos_intersect_data["gene_anno"]]
leftpos_intersect_data_diffgene = leftpos_intersect_data_diffgene.drop_duplicates(subset=[0,1,2,3],keep="first")

rightpos_intersect_data["gene_Examined"] = rightpos_intersect_data[3].str.split(":",expand=True)[0]
rightpos_intersect_data["gene_anno"] = rightpos_intersect_data[7].str.split(":",expand=True)[0]
rightpos_intersect_data_samegene = rightpos_intersect_data[rightpos_intersect_data["gene_Examined"]==rightpos_intersect_data["gene_anno"]]
rightpos_intersect_data_samegene = rightpos_intersect_data_samegene.drop_duplicates(subset=[0,1,2,3],keep="first")
rightpos_intersect_data_diffgene = rightpos_intersect_data[rightpos_intersect_data["gene_Examined"]!=rightpos_intersect_data["gene_anno"]]
rightpos_intersect_data_diffgene = rightpos_intersect_data_diffgene.drop_duplicates(subset=[0,1,2,3],keep="first")
########################################
########################################
inner_exon_intersect_data["len"] = inner_exon_intersect_data[6] - inner_exon_intersect_data[5]
inner_exon_intersect_data["intersect_ratio"] = inner_exon_intersect_data[9] / inner_exon_intersect_data["len"]
inner_exon_intersect_data["gene_Examined"] = inner_exon_intersect_data[3].str.split(":",expand=True)[0]
inner_exon_intersect_data["gene_anno"] = inner_exon_intersect_data[7].str.split(":",expand=True)[0]
inner_exon_intersect_data_samegene = inner_exon_intersect_data[inner_exon_intersect_data["gene_Examined"]==inner_exon_intersect_data["gene_anno"]]
inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene[inner_exon_intersect_data_samegene["intersect_ratio"]==1]
inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene.groupby([0,1,2,3]).agg({"intersect_ratio":"sum"}).reset_index()
inner_exon_intersect_data_diffgene = inner_exon_intersect_data[inner_exon_intersect_data["gene_Examined"]!=inner_exon_intersect_data["gene_anno"]]
inner_exon_intersect_data_diffgene = inner_exon_intersect_data_diffgene[inner_exon_intersect_data_diffgene["intersect_ratio"]==1]
inner_exon_intersect_data_diffgene = inner_exon_intersect_data_diffgene.groupby([0,1,2,3]).agg({"intersect_ratio":"sum"}).reset_index()

inner_intron_intersect_data["len"] = inner_intron_intersect_data[6] - inner_intron_intersect_data[5]
inner_intron_intersect_data["intersect_ratio"] = inner_intron_intersect_data[9] / inner_intron_intersect_data["len"]
inner_intron_intersect_data["gene_Examined"] = inner_intron_intersect_data[3].str.split(":",expand=True)[0]
inner_intron_intersect_data["gene_anno"] = inner_intron_intersect_data[7].str.split(":",expand=True)[0]
inner_intron_intersect_data_samegene = inner_intron_intersect_data[inner_intron_intersect_data["gene_Examined"]==inner_intron_intersect_data["gene_anno"]]
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene[inner_intron_intersect_data_samegene["intersect_ratio"]==1]
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene.groupby([0,1,2,3]).agg({"intersect_ratio":"sum"}).reset_index()
inner_intron_intersect_data_diffgene = inner_intron_intersect_data[inner_intron_intersect_data["gene_Examined"]!=inner_intron_intersect_data["gene_anno"]]
inner_intron_intersect_data_diffgene = inner_intron_intersect_data_diffgene[inner_intron_intersect_data_diffgene["intersect_ratio"]==1]
inner_intron_intersect_data_diffgene = inner_intron_intersect_data_diffgene.groupby([0,1,2,3]).agg({"intersect_ratio":"sum"}).reset_index()
#print(inner_intron_intersect_data_samegene)
#print(inner_intron_intersect_data_diffgene)
########################################
########################################
outdataframe = pd.merge(outdataframe,leftpos_intersect_data_samegene[[3,9]],how='left',on=[3])
outdataframe = pd.merge(outdataframe,leftpos_intersect_data_diffgene[[3,9]],how='left',on=[3])
outdataframe.columns = [0,1,2,3,"leftpos_samegene","leftpos_diffgene"]
outdataframe = pd.merge(outdataframe,rightpos_intersect_data_samegene[[3,9]],how='left',on=[3])
outdataframe = pd.merge(outdataframe,rightpos_intersect_data_diffgene[[3,9]],how='left',on=[3])
outdataframe.columns = [0,1,2,3,"leftpos_samegene","leftpos_diffgene","rightpos_samegene","rightpos_diffgene"]
outdataframe = pd.merge(outdataframe,inner_exon_intersect_data_samegene[[3,"intersect_ratio"]],how='left',on=[3])
outdataframe = pd.merge(outdataframe,inner_exon_intersect_data_diffgene[[3,"intersect_ratio"]],how='left',on=[3])
outdataframe.columns = [0,1,2,3,"leftpos_samegene","leftpos_diffgene","rightpos_samegene","rightpos_diffgene","inner_exon_samegene","inner_exon_diffgene"]
outdataframe = pd.merge(outdataframe,inner_intron_intersect_data_samegene[[3,"intersect_ratio"]],how='left',on=[3])
outdataframe = pd.merge(outdataframe,inner_intron_intersect_data_diffgene[[3,"intersect_ratio"]],how='left',on=[3])
outdataframe.columns = ["chr","start","end","Alt_Splice","leftpos_samegene","leftpos_diffgene","rightpos_samegene","rightpos_diffgene","inner_exon_samegene","inner_exon_diffgene","inner_intron_samegene","inner_intron_diffgene"]
outdataframe = outdataframe.fillna(0)
outdataframe = outdataframe[["Alt_Splice","leftpos_samegene","leftpos_diffgene","rightpos_samegene","rightpos_diffgene","inner_exon_samegene","inner_exon_diffgene","inner_intron_samegene","inner_intron_diffgene"]]
########################################
########################################
########################################

pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
#################################################################################
ID_Data_cassette = ID_Data[ID_Data["EventAnnotation"]=="cassette-exon"].head(200)
ID_Data_cassette = pd.merge(ID_Data_cassette,outdataframe,how='inner',on="Alt_Splice")
outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.SampleSplice_Exon_Anno_Merge_Top_cassette_anno.txt"
ID_Data_cassette.to_csv(outfile, sep="\t", index=False, header=True)

print(outdataframe)
print(ID_Data_cassette)