#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Exon_Skipping.py
@Time    :   2025/07/08 16:52:45
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
ID_Data = ID_Data[ID_Data["EventAnnotation"]=="cassette-exon"]
ID_Data[["chr","start","end"]] = ID_Data["pos_IGV"].str.split(':|-', expand=True)

ID_Data_Region = ID_Data[["chr","start","end","Alt_Splice","pos_Examined","pos_IGV"]]
ID_Data_Region = ID_Data_Region.drop_duplicates()
ID_Data_Region["start_left"] = (ID_Data_Region["start"].astype(int)) - 1  
ID_Data_Region["start_right"] = (ID_Data_Region["end"].astype(int)) - 1  

print(ID_Data_Region)
bed_data_left =  ID_Data_Region[["chr","start_left", "start", "Alt_Splice"]]
bed_data_inner =  ID_Data_Region[["chr","start", "start_right", "Alt_Splice"]]
bed_data_right =  ID_Data_Region[["chr","start_right", "end", "Alt_Splice"]]

outfile1 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_leftpos.txt"
outfile2 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_innerpos.txt"
outfile3 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_rightpos.txt"

bed_data_left.to_csv(outfile1,sep="\t",header=False,index=False)
bed_data_inner.to_csv(outfile2,sep="\t",header=False,index=False)
bed_data_right.to_csv(outfile3,sep="\t",header=False,index=False)


print(bed_data_left)
print(bed_data_inner)
print(bed_data_right)



'''
awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$3=$2+1} 1' exon.bed > exon.leftpos.bed
awk 'BEGIN{FS=OFS="\t"} !/^#/ && NF>=3{$2=$3-1} 1' exon.bed > exon.rightpos.bed


bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_leftpos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.rightpos.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_leftpos_intersect.txt

bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_rightpos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.leftpos.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_rightpos_intersect.txt

bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_innerpos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_inner_exon_intersect.txt

bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_innerpos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/intron.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.Exon_Skipping_inner_intron_intersect.txt

'''

