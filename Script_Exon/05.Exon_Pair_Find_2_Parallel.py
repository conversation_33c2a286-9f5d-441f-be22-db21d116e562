import pandas as pd
import re
import string
import numpy as np
from multiprocessing import Pool, cpu_count
import os
from functools import partial
import time

def process_chunk(chunk_data, bed_dir):
    """
    处理数据块的函数
    """
    # 为每个chunk创建结果列表
    results = []
    
    # 缓存已读取的bed文件，避免重复读取
    bed_cache = {}
    
    for i in chunk_data.index:
        Alt_Splice = chunk_data.loc[i,"Alt_Splice"]
        pos_Examined = chunk_data.loc[i,"pos_Examined"]
        SampleInfo = chunk_data.loc[i,"SampleID"]
        SupportReads_1 = chunk_data.loc[i,"SupportReads"]
        gene_name = re.split(":",Alt_Splice)[0]
        pos_info = re.split(":|-",pos_Examined)
        SampleID = re.split("\.",SampleInfo)[0]
        chr_info = pos_info[0]
        pos_1 = pos_info[1]
        pos_2 = pos_info[2]
        
        # 按照原始脚本逻辑：总是先尝试读取intronJunction.bed文件
        SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__intronJunction.bed"

        # 使用缓存避免重复读取同一个文件
        cache_key = (SampleFile, False)  # intronJunction.bed不使用skiprows
        if cache_key not in bed_cache:
            try:
                SampleData = pd.read_csv(SampleFile, sep="\t", header=None)
                SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
                SampleData["Strand"] = SampleData[5]
                bed_cache[cache_key] = SampleData
            except Exception as e:
                print(f"Error reading file {SampleFile}: {e}")
                results.append((i, "-", "-"))
                continue
        else:
            SampleData = bed_cache[cache_key]
        
        # 处理数据逻辑 - 完全按照原始脚本逻辑
        if abs(int(pos_1) - int(pos_2)) == 1:
            SampleData_1 = SampleData[SampleData[0]==chr_info]
            SampleData_1 = SampleData_1[SampleData_1["Pos"].isin([pos_1,pos_2])]
            if len(SampleData_1) > 1:
                SampleData_1 = SampleData_1[SampleData_1["Gene"]==gene_name]
        else:
            # 重新读取junction.bed文件（与原始脚本逻辑一致）
            SampleFile = f"{bed_dir}/{SampleID}.Aligned.sortedByCoord.out__junction.bed"

            # 使用缓存避免重复读取同一个文件
            cache_key = (SampleFile, True)  # junction.bed使用skiprows=1
            if cache_key not in bed_cache:
                try:
                    SampleData = pd.read_csv(SampleFile, sep="\t", header=None, skiprows=1)
                    SampleData[["Gene","Exon","Pos"]] = SampleData[3].str.split('-|:', expand=True)
                    SampleData["Strand"] = SampleData[5]
                    bed_cache[cache_key] = SampleData
                except Exception as e:
                    print(f"Error reading file {SampleFile}: {e}")
                    results.append((i, "-", "-"))
                    continue
            else:
                SampleData = bed_cache[cache_key]

            SampleData_1 = SampleData[SampleData[0]==chr_info]
            SampleData_1 = SampleData[SampleData["Exon"]==pos_1]
            SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_2]
            if len(SampleData_1) == 0:
                SampleData_1 = SampleData[SampleData[0]==chr_info]
                SampleData_1 = SampleData[SampleData["Exon"]==pos_2]
                SampleData_1 = SampleData_1[SampleData_1["Pos"]==pos_1]
        
        SampleData_1 = SampleData_1[SampleData_1[4]==SupportReads_1]
        
        if len(SampleData_1) == 1:
            SampleData_1["IntronInfo"] = SampleData_1["Gene"] + ":" + SampleData_1["Exon"]
            SupportReads_2 = SampleData_1[4].values[0]
            raw_id = SampleData_1[3].values[0]
            raw_gene_exon = SampleData_1["IntronInfo"].values[0]
            strand = SampleData_1["Strand"].values[0]
        else:
            raw_id = "-"
            raw_gene_exon = "-"
            strand = "-"
        
        results.append((i, raw_id, raw_gene_exon, strand))
    
    return results

def main():
    """
    主函数：实现并行处理
    """
    print("开始处理IR数据...")
    start_time = time.time()
    
    # 读取数据
    IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
    bed_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/bed"
    
    print("读取IR数据文件...")
    ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
    ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
    ID_Data = ID_Data.sort_values(by="SampleID", ascending=False)
    
    # 添加新列
    ID_Data["Raw_ID"] = "-"
    ID_Data["IntronInfo"] = "-"
    
    print(f"总共需要处理 {len(ID_Data)} 条记录")
    
    # 分割数据为24块
    n_chunks = 24
    chunk_size = len(ID_Data) // n_chunks
    chunks = []
    
    for i in range(n_chunks):
        start_idx = i * chunk_size
        if i == n_chunks - 1:  # 最后一块包含剩余的所有数据
            end_idx = len(ID_Data)
        else:
            end_idx = (i + 1) * chunk_size
        
        chunk = ID_Data.iloc[start_idx:end_idx].copy()
        chunks.append(chunk)
        print(f"块 {i+1}: {len(chunk)} 条记录 (索引 {start_idx} 到 {end_idx-1})")
    
    # 并行处理
    print(f"开始并行处理，使用 {n_chunks} 个进程...")
    process_func = partial(process_chunk, bed_dir=bed_dir)
    
    with Pool(processes=n_chunks) as pool:
        chunk_results = pool.map(process_func, chunks)
    
    # 合并结果
    print("合并处理结果...")
    for chunk_result in chunk_results:
        for idx, raw_id, intron_info, strand in chunk_result:
            ID_Data.loc[idx, "Raw_ID"] = raw_id
            ID_Data.loc[idx, "IntronInfo"] = intron_info
            ID_Data.loc[idx, "strand"] = strand
    
    # 保存结果
    out_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
    ID_Data.to_csv(out_File, header=True, index=False, sep="\t")
    
    end_time = time.time()
    print(f"处理完成！总耗时: {end_time - start_time:.2f} 秒")
    print(f"结果已保存到: {out_File}")

if __name__ == "__main__":
    main()
