#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   09.Alt_C_2.py
@Time    :   2025/07/11 11:20:53
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon"
alt_splice_unchange =  f"{analysis_dir}/09.Alt-C-5pos-intersect-3.txt"
alt_splice_change =  f"{analysis_dir}/09.Alt-C-3pos-intersect-5.txt"
alt_splice_last_exon = f"{analysis_dir}/09.Alt-C-inner-intersect-lastExon.txt"
alt_splice_exon = f"{analysis_dir}/09.Alt-C-inner-intersect-Exon.txt"


alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
alt_splice_last_exon_data = pd.read_csv(alt_splice_last_exon, sep="\t", header=None, skiprows=0)
alt_splice_exon_data = pd.read_csv(alt_splice_exon, sep="\t", header=None, skiprows=0)

outdataframe = alt_splice_unchange_data[[3]]
outdataframe = outdataframe.drop_duplicates()

#########################################################################################3
alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[7].str.split(":",expand=True)
alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

alt_splice_change_data["gene_Examined"] = alt_splice_change_data[3].str.split(":",expand=True)[0]
alt_splice_change_data[["gene_anno","trans_anno"]] = alt_splice_change_data[7].str.split(":",expand=True)
alt_splice_change_data_samegene = alt_splice_change_data[alt_splice_change_data["gene_Examined"]==alt_splice_change_data["gene_anno"]]
alt_splice_change_data_samegene = alt_splice_change_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
#########################################################################################
print(alt_splice_unchange_data_samegene)
print(alt_splice_change_data_samegene)
#########################################################################################
alt_splice_last_exon_data["len"] = alt_splice_last_exon_data[6] - alt_splice_last_exon_data[5]
alt_splice_last_exon_data["intersect_ratio"] = alt_splice_last_exon_data[8] / alt_splice_last_exon_data["len"]
alt_splice_last_exon_data["gene_Examined"] = alt_splice_last_exon_data[3].str.split(":",expand=True)[0]
alt_splice_last_exon_data[["gene_anno","trans_anno"]] = alt_splice_last_exon_data[7].str.split(":",expand=True)
alt_splice_last_exon_data_samegene = alt_splice_last_exon_data[alt_splice_last_exon_data["gene_Examined"]==alt_splice_last_exon_data["gene_anno"]]
alt_splice_last_exon_data_samegene = alt_splice_last_exon_data_samegene[alt_splice_last_exon_data_samegene["intersect_ratio"]==1]
alt_splice_last_exon_data_samegene = alt_splice_last_exon_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
#########################################################################################
print(alt_splice_unchange_data_samegene)
alt_splice_exon_data["gene_Examined"] = alt_splice_exon_data[3].str.split(":",expand=True)[0]
alt_splice_exon_data[["gene_anno","trans_anno"]] = alt_splice_exon_data[7].str.split(":",expand=True)
alt_splice_exon_data = alt_splice_exon_data[alt_splice_exon_data["gene_Examined"]==alt_splice_exon_data["gene_anno"]]
alt_splice_exon_data = alt_splice_exon_data[alt_splice_exon_data[9]>1]
alt_splice_exon_data = alt_splice_exon_data.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

print(alt_splice_exon_data)
#########################################################################################
print(outdataframe)
outdataframe.columns = ["Alt_Splice"]
alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
alt_splice_change_data_samegene.columns = ["Alt_Splice","ChangeInfo","ChangeTrans"]
alt_splice_last_exon_data_samegene.columns = ["Alt_Splice","LastExonInfo","LastExonTrans"]
alt_splice_exon_data.columns = ["Alt_Splice","ExonTrans"]
outdataframe = pd.merge(outdataframe,alt_splice_unchange_data_samegene,on="Alt_Splice",how='left')
outdataframe = pd.merge(outdataframe,alt_splice_change_data_samegene,on="Alt_Splice",how='left')
outdataframe = pd.merge(outdataframe,alt_splice_last_exon_data_samegene,on="Alt_Splice",how='left')
outdataframe = pd.merge(outdataframe,alt_splice_exon_data,on="Alt_Splice",how='left')
outdataframe = outdataframe.fillna(0)
print(outdataframe)
#########################################################################################
outdataframe["Trans"] = "NO"
outdataframe["TransDetail"] = "-"
for i in outdataframe.index:
    Trans = "NO"
    TransDetail = "-"
    UnChangeTrans = re.split("\|",str(outdataframe.loc[i,"UnChangeTrans"]))
    UnChangeInfo = outdataframe.loc[i,"UnChangeInfo"]
    ChangeTrans = re.split("\|",str(outdataframe.loc[i,"ChangeTrans"]))
    ChangeInfo = outdataframe.loc[i,"ChangeInfo"]
    LastExonTrans = re.split("\|",str(outdataframe.loc[i,"LastExonTrans"]))
    LastExonInfo = outdataframe.loc[i,"LastExonInfo"]
    ExonTrans = re.split("\|",str(outdataframe.loc[i,"ExonTrans"]))
    if UnChangeInfo == 1:
        if ChangeInfo == 0:
            if LastExonInfo == 1:
                common_elements = set(UnChangeTrans) & set(LastExonTrans)
                if len(common_elements) > 0:
                    diff_elements = set(ChangeTrans) - common_elements
                    if len(diff_elements) > 0:
                        Trans = "YES"
                        TransDetail = "-"
            if LastExonInfo == 0:
                common_elements = set(UnChangeTrans) & set(LastExonTrans) & set(ChangeTrans)
                if len(common_elements) > 0:
                    Trans = "YES"
                    TransDetail = "-"
    outdataframe.loc[i,"Trans"] = Trans
    outdataframe.loc[i,"TransDetail"] = TransDetail
#########################################################################################
#########################################################################################
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir

ID_Data_altC = ID_Data[ID_Data["EventAnnotation"]=="alt-C-term"]
ID_Data_altC = pd.merge(ID_Data_altC,outdataframe,how='left',on="Alt_Splice")
#################################################################################
print(ID_Data_altC)
AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="alt-C-term"]
anno_data = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","CancerSampleRatio","dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data["Diff_Gtex"] = anno_data["Cancer_Mean"] - anno_data["Gtex_Mean"]
anno_data["Diff_TCGA"] = anno_data["Cancer_Mean"] - anno_data["TCGA_Mean"]
anno_data["Diff_Skin"] = anno_data["Cancer_Mean"] - anno_data["Skin_Mean"]
ID_Data_altC = pd.merge(ID_Data_altC,anno_data,how='inner',on="Alt_Splice")

outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C_anno.txt"
ID_Data_altC.to_csv(outfile, sep="\t", index=False, header=True)

print(ID_Data_altC)