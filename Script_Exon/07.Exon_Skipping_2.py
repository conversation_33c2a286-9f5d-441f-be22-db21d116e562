#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.Exon_Skipping_2.py
@Time    :   2025/07/08 17:40:44
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon"
leftpos_intersect_file = f"{analysis_dir}/07.Exon_Skipping_leftpos_intersect.txt"
rightpos_intersect_file = f"{analysis_dir}/07.Exon_Skipping_rightpos_intersect.txt"
inner_exon_intersect_file = f"{analysis_dir}/07.Exon_Skipping_inner_exon_intersect.txt"
inner_intron_intersect_file = f"{analysis_dir}/07.Exon_Skipping_inner_intron_intersect.txt"

leftpos_intersect_data = pd.read_csv(leftpos_intersect_file, sep="\t", header=None, skiprows=0)
rightpos_intersect_data = pd.read_csv(rightpos_intersect_file, sep="\t", header=None, skiprows=0)
inner_exon_intersect_data = pd.read_csv(inner_exon_intersect_file, sep="\t", header=None, skiprows=0)
inner_intron_intersect_data = pd.read_csv(inner_intron_intersect_file, sep="\t", header=None, skiprows=0)
########################################
########################################

outdataframe = leftpos_intersect_data[[3]]
outdataframe = outdataframe.drop_duplicates()

########################################
########################################
leftpos_intersect_data["gene_Examined"] = leftpos_intersect_data[3].str.split(":",expand=True)[0]
leftpos_intersect_data[["gene_anno","trans_anno"]] = leftpos_intersect_data[7].str.split(":",expand=True)
leftpos_intersect_data_samegene = leftpos_intersect_data[leftpos_intersect_data["gene_Examined"]==leftpos_intersect_data["gene_anno"]]
leftpos_intersect_data_samegene = leftpos_intersect_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

rightpos_intersect_data["gene_Examined"] = rightpos_intersect_data[3].str.split(":",expand=True)[0]
rightpos_intersect_data[["gene_anno","trans_anno"]] = rightpos_intersect_data[7].str.split(":",expand=True)
rightpos_intersect_data_samegene = rightpos_intersect_data[rightpos_intersect_data["gene_Examined"]==rightpos_intersect_data["gene_anno"]]
rightpos_intersect_data_samegene = rightpos_intersect_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
########################################
########################################
inner_exon_intersect_data["len"] = inner_exon_intersect_data[6] - inner_exon_intersect_data[5]
inner_exon_intersect_data["intersect_ratio"] = inner_exon_intersect_data[9] / inner_exon_intersect_data["len"]
inner_exon_intersect_data["gene_Examined"] = inner_exon_intersect_data[3].str.split(":",expand=True)[0]
inner_exon_intersect_data[["gene_anno","trans_anno"]] = inner_exon_intersect_data[7].str.split(":",expand=True)
inner_exon_intersect_data_samegene = inner_exon_intersect_data[inner_exon_intersect_data["gene_Examined"]==inner_exon_intersect_data["gene_anno"]]
inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene[inner_exon_intersect_data_samegene["intersect_ratio"]==1]
inner_exon_intersect_data_samegene = inner_exon_intersect_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

inner_intron_intersect_data["len"] = inner_intron_intersect_data[6] - inner_intron_intersect_data[5]
inner_intron_intersect_data["intersect_ratio"] = inner_intron_intersect_data[9] / inner_intron_intersect_data["len"]
inner_intron_intersect_data["gene_Examined"] = inner_intron_intersect_data[3].str.split(":",expand=True)[0]
inner_intron_intersect_data[["gene_anno","trans_anno"]] = inner_intron_intersect_data[7].str.split(":",expand=True)
inner_intron_intersect_data_samegene = inner_intron_intersect_data[inner_intron_intersect_data["gene_Examined"]==inner_intron_intersect_data["gene_anno"]]
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene[inner_intron_intersect_data_samegene["intersect_ratio"]==1]
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene.groupby([3,"trans_anno"]).agg({"intersect_ratio":"sum"}).reset_index()
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene[inner_intron_intersect_data_samegene["intersect_ratio"]>=2]
inner_intron_intersect_data_samegene = inner_intron_intersect_data_samegene.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
inner_intron_intersect_data_samegene["intersect_ratio"] = 2

#print(inner_intron_intersect_data_samegene)
#print(inner_intron_intersect_data_diffgene)
########################################
########################################
outdataframe = pd.merge(outdataframe,leftpos_intersect_data_samegene,how='left',on=[3])
outdataframe = pd.merge(outdataframe,rightpos_intersect_data_samegene,how='left',on=[3])
outdataframe.columns = [3,"leftpos_samegene","leftpos_samegene_trans","rightpos_samegene","rightpos_samegene_trans"]
outdataframe = pd.merge(outdataframe,inner_exon_intersect_data_samegene,how='left',on=[3])
outdataframe = pd.merge(outdataframe,inner_intron_intersect_data_samegene,how='left',on=[3])
outdataframe.columns = ["Alt_Splice","leftpos_samegene","leftpos_samegene_trans","rightpos_samegene","rightpos_samegene_trans","inner_exon_samegene","inner_exon_samegene_trans","inner_intron_samegene_trans","inner_intron_samegene"]

outdataframe = outdataframe.fillna(0)
print(outdataframe)
outdataframe["Trans"] = "YES"
outdataframe["TransDetail"] = "YES"
#outdataframe = outdataframe.head(10)
for i in outdataframe.index:
    leftpos_samegene_trans = re.split("\|",str(outdataframe.loc[i,"leftpos_samegene_trans"]))
    rightpos_samegene_trans = re.split("\|",str(outdataframe.loc[i,"rightpos_samegene_trans"]))
    inner_exon_samegene_trans = re.split("\|",str(outdataframe.loc[i,"inner_exon_samegene_trans"]))
    inner_intron_samegene_trans = re.split("\|",str(outdataframe.loc[i,"inner_intron_samegene_trans"]))
    common_elements = set(leftpos_samegene_trans) & set(rightpos_samegene_trans) & set(inner_exon_samegene_trans) & set(inner_intron_samegene_trans)
    common_elements = list(common_elements)
    if "0" in common_elements:
        common_elements.remove("0") 
    if len(common_elements) > 0:
        Trans = "YES"
        TransDetail = "|".join(common_elements)
    else:
        Trans = "NO"
        TransDetail = "-"
    outdataframe.loc[i,"Trans"] = Trans
    outdataframe.loc[i,"TransDetail"] = TransDetail
    #print(common_elements)
    #print("############################3")
#outdataframe = outdataframe[["Alt_Splice","leftpos_samegene","rightpos_samegene","inner_exon_samegene","inner_intron_samegene","Trans","TransDetail"]]


pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
#################################################################################
ID_Data_cassette = ID_Data[ID_Data["EventAnnotation"]=="cassette-exon"]
ID_Data_cassette = pd.merge(ID_Data_cassette,outdataframe,how='inner',on="Alt_Splice")
#################################################################################

AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="cassette-exon"]
anno_data = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","CancerSampleRatio","dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data["Diff_Gtex"] = anno_data["Cancer_Mean"] - anno_data["Gtex_Mean"]
anno_data["Diff_TCGA"] = anno_data["Cancer_Mean"] - anno_data["TCGA_Mean"]
anno_data["Diff_Skin"] = anno_data["Cancer_Mean"] - anno_data["Skin_Mean"]
#################################################################################
ID_Data_cassette = pd.merge(ID_Data_cassette,anno_data,how='inner',on="Alt_Splice")

outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/07.SampleSplice_Exon_Anno_Merge_Top_cassette_anno.txt"
ID_Data_cassette.to_csv(outfile, sep="\t", index=False, header=True)

#print(outdataframe)
#print(ID_Data_cassette)
