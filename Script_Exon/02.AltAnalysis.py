#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   07.IR_Diff_Top.py
@Time    :   2025/06/30 13:10:39
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
import numpy as np

neojunction_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/08.SNAF_T/NeoJunction_statistics_maxmin.txt"
neojunction_data = pd.read_csv(neojunction_file,sep="\t",header=0,skiprows=0)
neojunction_data = neojunction_data.rename(columns={'Unnamed: 0': 'Alt_Splice'})
neojunction_data = neojunction_data[neojunction_data["cond"]==True]
neojunction_data = neojunction_data[neojunction_data["cond_add_tcga_control"]==True]
neojunction_data = neojunction_data[neojunction_data["cond_add_gtex_skin"]==True]

#The numerical value represents the number of reads that support the occurence of a certain junction
purned_counts_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/ExpressionInput/counts.original.pruned.txt"
purned_counts_data = pd.read_csv(purned_counts_file,sep="\t",header=0,skiprows=0)
purned_counts_data = purned_counts_data.rename(columns={'Unnamed: 0': 'Alt_Splice'})
purned_counts_data = purned_counts_data[purned_counts_data["Alt_Splice"].isin(neojunction_data["Alt_Splice"].values)]

#The numerical value represents the number of reads that support the occurence of a certain junction
purned_tpm_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/ExpressionInput/exp.original.txt"
purned_tpm_data = pd.read_csv(purned_tpm_file,sep="\t",header=0,skiprows=0)
purned_tpm_data = purned_tpm_data.rename(columns={'AltAnalyze_ID': 'Alt_Splice'})
purned_tpm_data = purned_tpm_data[purned_tpm_data["Alt_Splice"].isin(neojunction_data["Alt_Splice"].values)]
#print(purned_tpm_data)
purned_PSI_file = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/05.Clinical_Sample_Analysis/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/07.AltAnalyze/altanalyze_output/AltResults/AlternativeOutput/Hs_RNASeq_top_alt_junctions-PSI.txt"
purned_PSI_data = pd.read_csv(purned_PSI_file,sep="\t",header=0,skiprows=0)
purned_PSI_data = purned_PSI_data.rename(columns={'Examined-Junction': 'Alt_Splice'})
purned_PSI_data = purned_PSI_data[purned_PSI_data["Alt_Splice"].isin(neojunction_data["Alt_Splice"].values)]
print(purned_PSI_data)
purned_PSI_data = purned_PSI_data.drop(["Symbol","Description","Background-Major-Junction","AltExons","PME","dPSI","ClusterID","UID","Coordinates","feature"],axis=1)
print(purned_PSI_data)

AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data.dropna(subset=['EventAnnotation'])
anno_data = anno_data[anno_data["EventAnnotation"]!="intron-retention"]
anno_data = anno_data.sort_values(by="EventAnnotation",ascending=True)
print(anno_data)
anno_data_top = anno_data
sample_alt_splice = []
for i in anno_data_top.index:
    Alt_Splice = anno_data_top.loc[i,"Alt_Splice"]
    Symbol = anno_data_top.loc[i,"Symbol"]
    EventAnnotation = anno_data_top.loc[i,"EventAnnotation"]
    pos_Examined = anno_data_top.loc[i,"pos_Examined"]
    pos_BKG = anno_data_top.loc[i,"pos_BKG"]
    bkg_major = anno_data_top.loc[i,"Background-Major-Junction"]
    AltExons = anno_data_top.loc[i,"AltExons"]
    purned_counts_data_sub = purned_counts_data[purned_counts_data["Alt_Splice"]==Alt_Splice]
    purned_counts_data_sub = purned_counts_data_sub.drop("Alt_Splice",axis=1)
    purned_counts_data_sub = purned_counts_data_sub.T
    purned_counts_data_sub.columns = ["SupportReads"]
    purned_counts_data_sub = purned_counts_data_sub[purned_counts_data_sub["SupportReads"]>0]
    purned_counts_data_sub["Alt_Splice"] = Alt_Splice
    purned_counts_data_sub["Symbol"] = Symbol
    purned_counts_data_sub["EventAnnotation"] = EventAnnotation
    purned_counts_data_sub["pos_Examined"] = pos_Examined
    purned_counts_data_sub["pos_BKG"] = pos_BKG
    purned_counts_data_sub["Background-Major-Junction"] = bkg_major
    purned_counts_data_sub["AltExons"] = AltExons
    purned_counts_data_sub["SampleID"] = purned_counts_data_sub.index
    #print(purned_counts_data_sub)
    purned_tpm_data_sub = purned_tpm_data[purned_tpm_data["Alt_Splice"]==Alt_Splice]
    purned_tpm_data_sub = purned_tpm_data_sub.drop("Alt_Splice",axis=1)
    purned_tpm_data_sub = purned_tpm_data_sub.T
    purned_tpm_data_sub['mean'] = purned_tpm_data_sub.mean(axis=1)
    purned_tpm_data_sub = purned_tpm_data_sub[["mean"]]
    purned_tpm_data_sub.columns = ["SupportRPKM"]
    purned_tpm_data_sub["SampleID"] = purned_tpm_data_sub.index
    ######
    purned_PSI_data_sub = purned_PSI_data[purned_PSI_data["Alt_Splice"]==Alt_Splice]
    purned_PSI_data_sub = purned_PSI_data_sub.drop("Alt_Splice",axis=1)
    purned_PSI_data_sub = purned_PSI_data_sub.T
    purned_PSI_data_sub.columns = ["MultiPath-PSI"]
    purned_PSI_data_sub["SampleID"] = purned_PSI_data_sub.index
    ######
    purned_counts_data_sub = pd.merge(purned_counts_data_sub,purned_tpm_data_sub,on="SampleID",how='left')
    purned_counts_data_sub = pd.merge(purned_counts_data_sub,purned_PSI_data_sub,on="SampleID",how='left')
    purned_counts_data_sub = purned_counts_data_sub[["Alt_Splice","SampleID","Symbol","EventAnnotation","Background-Major-Junction","AltExons","pos_Examined","pos_BKG","SupportReads","SupportRPKM","MultiPath-PSI"]]
    purned_counts_data_sub = purned_counts_data_sub.sort_values(by="SupportReads",ascending=False)
    #print(purned_counts_data_sub)
    if len(sample_alt_splice) == 0:
        sample_alt_splice = purned_counts_data_sub
    else:
        sample_alt_splice = pd.concat([sample_alt_splice,purned_counts_data_sub],ignore_index=True)
    ######
print(sample_alt_splice)
outfile2 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
sample_alt_splice.to_csv(outfile2,sep="\t",header=True,index=False)
