#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   08.Alt_3.py
@Time    :   2025/07/09 15:12:44
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
ID_Data[["chr"]] = "-"
ID_Data[["start"]] = "-"
ID_Data[["end"]] = "-"
ID_Data[["start_5"]] = "-"
ID_Data[["end_5"]] = "-"
ID_Data[["start_3"]] = "-"
ID_Data[["end_3"]] = "-"

for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    strand = ID_Data.loc[i,"strand"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
        start = pos_2
        end = pos_1
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
        start = pos_1
        end = pos_2
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
    ID_Data.loc[i,"start"] = start
    ID_Data.loc[i,"end"] = end
    start_5 = int(pos_1) - 1
    end_5 = int(pos_1)
    start_3 = int(pos_2) - 1
    end_3 = int(pos_2)
    ID_Data.loc[i,"chr"] = chr
    ID_Data.loc[i,"start_5"] = start_5
    ID_Data.loc[i,"end_5"] = end_5
    ID_Data.loc[i,"start_3"] = start_3
    ID_Data.loc[i,"end_3"] = end_3

ID_Data_altC = ID_Data[ID_Data["EventAnnotation"]=="alt-C-term"]
#ID_Data_altP = ID_Data[ID_Data["EventAnnotation"]=="altPromoter"]
#ID_Data_altT = ID_Data[ID_Data["EventAnnotation"]=="trans-splicing"]
print(ID_Data_altC)
#print(ID_Data_altP)
#print(ID_Data_altT)
outfile1 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-5pos.txt"
outfile2 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-3pos.txt"
outfile3 = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-inner.txt"

print(ID_Data)
ID_Data_altC[["chr","start_5", "end_5", "Alt_Splice"]].to_csv(outfile1,sep="\t",header=False,index=False)
ID_Data_altC[["chr","start_3", "end_3", "Alt_Splice"]].to_csv(outfile2,sep="\t",header=False,index=False)
ID_Data_altC[["chr","start", "end", "Alt_Splice"]].to_csv(outfile3,sep="\t",header=False,index=False)

'''
bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-5pos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon-3-start.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-5pos-intersect-3.txt
bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-3pos.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon-5-start.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-3pos-intersect-5.txt
bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-inner.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon_transcript_last-exon.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-inner-intersect-lastExon.txt
bedtools intersect -a /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-inner.txt -b /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.bed -wao > /ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/09.Alt-C-inner-intersect-Exon.txt

'''
