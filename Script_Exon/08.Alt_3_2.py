#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   08.Alt_3_2.py
@Time    :   2025/07/09 16:05:21
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import pandas as pd
import re
import numpy as np
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon"
alt_splice_unchange =  f"{analysis_dir}/08.Alt-3-5pos-intersect-3.txt"
alt_splice_change =  f"{analysis_dir}/08.Alt-3-3pos-intersect-5.txt"
alt_splice_inner_intron = f"{analysis_dir}/08.Alt-3-inner-intersect-intron.txt"
alt_splice_inner_exon = f"{analysis_dir}/08.Alt-3-inner-intersect-exon.txt"

alt_splice_unchange_data = pd.read_csv(alt_splice_unchange, sep="\t", header=None, skiprows=0)
alt_splice_change_data = pd.read_csv(alt_splice_change, sep="\t", header=None, skiprows=0)
alt_splice_inner_intron_data = pd.read_csv(alt_splice_inner_intron, sep="\t", header=None, skiprows=0)
alt_splice_inner_exon_data = pd.read_csv(alt_splice_inner_exon, sep="\t", header=None, skiprows=0)

alt_splice_unchange_data["gene_Examined"] = alt_splice_unchange_data[3].str.split(":",expand=True)[0]
alt_splice_unchange_data[["gene_anno","trans_anno"]] = alt_splice_unchange_data[7].str.split(":",expand=True)
alt_splice_unchange_data_samegene = alt_splice_unchange_data[alt_splice_unchange_data["gene_Examined"]==alt_splice_unchange_data["gene_anno"]]
alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
#alt_splice_unchange_data_samegene = alt_splice_unchange_data_samegene.groupby([3,9])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

alt_splice_change_data = alt_splice_change_data[[3,9]]
alt_splice_change_data = alt_splice_change_data.drop_duplicates()
#alt_splice_change_data.columns = ["Alt_Splice","ChangeSite"]
#print(alt_splice_change_data)

alt_splice_inner_intron_data["len"] = alt_splice_inner_intron_data[6] - alt_splice_inner_intron_data[5]
alt_splice_inner_intron_data["intersect_ratio"] = alt_splice_inner_intron_data[9] / alt_splice_inner_intron_data["len"]
alt_splice_inner_intron_data["gene_Examined"] = alt_splice_inner_intron_data[3].str.split(":",expand=True)[0]
alt_splice_inner_intron_data[["gene_anno","trans_anno"]] = alt_splice_inner_intron_data[7].str.split(":",expand=True)
alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data[alt_splice_inner_intron_data["gene_Examined"]==alt_splice_inner_intron_data["gene_anno"]]
alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene[alt_splice_inner_intron_data_samegene["intersect_ratio"]==1]
alt_splice_inner_intron_data_samegene = alt_splice_inner_intron_data_samegene.groupby([3,"intersect_ratio"])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()

alt_splice_inner_exon_data["len"] = alt_splice_inner_exon_data[6] - alt_splice_inner_exon_data[5]
alt_splice_inner_exon_data["intersect_ratio"] = alt_splice_inner_exon_data[9] / alt_splice_inner_exon_data["len"]
alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data["intersect_ratio"]<1]
alt_splice_inner_exon_data = alt_splice_inner_exon_data[alt_splice_inner_exon_data[9]>=2]
alt_splice_inner_exon_data["gene_Examined"] = alt_splice_inner_exon_data[3].str.split(":",expand=True)[0]
alt_splice_inner_exon_data[["gene_anno","trans_anno"]] = alt_splice_inner_exon_data[7].str.split(":",expand=True)
alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data[alt_splice_inner_exon_data["gene_Examined"]==alt_splice_inner_exon_data["gene_anno"]]
alt_splice_inner_exon_data_samegene = alt_splice_inner_exon_data_samegene.groupby([3])["trans_anno"].agg(lambda x: '|'.join(x)).reset_index()
alt_splice_inner_exon_data_samegene["ExonIntersect"] = 2

alt_splice_change_data.columns = ["Alt_Splice","ChangeInfo"]
alt_splice_unchange_data_samegene.columns = ["Alt_Splice","UnChangeInfo","UnChangeTrans"]
alt_splice_inner_intron_data_samegene.columns = ["Alt_Splice","IntronIntersect","IntronTrans"]
alt_splice_inner_exon_data_samegene.columns = ["Alt_Splice","ExonTrans","ExonIntersect"]
alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_unchange_data_samegene,on='Alt_Splice',how='left')
alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_intron_data_samegene,on='Alt_Splice',how='left')
alt_splice_change_data = pd.merge(alt_splice_change_data,alt_splice_inner_exon_data_samegene,on='Alt_Splice',how='left')
alt_splice_change_data = alt_splice_change_data.fillna(0)
alt_splice_change_data["Trans"] = "YES"
alt_splice_change_data["TransDetail"] = "YES"
#outdataframe = outdataframe.head(10)
for i in alt_splice_change_data.index:
    UnChangeTrans = re.split("\|",str(alt_splice_change_data.loc[i,"UnChangeTrans"]))
    IntronTrans = re.split("\|",str(alt_splice_change_data.loc[i,"IntronTrans"]))
    ExonTrans = re.split("\|",str(alt_splice_change_data.loc[i,"ExonTrans"]))
    common_elements = set(UnChangeTrans) & set(IntronTrans) & set(ExonTrans)
    common_elements = list(common_elements)
    if "0" in common_elements:
        common_elements.remove("0") 
    if len(common_elements) > 0:
        Trans = "YES"
        TransDetail = "|".join(common_elements)
    else:
        Trans = "NO"
        TransDetail = "-"
    alt_splice_change_data.loc[i,"Trans"] = Trans
    alt_splice_change_data.loc[i,"TransDetail"] = TransDetail

print(alt_splice_change_data)
#print(alt_splice_unchange_data_samegene)
#print(alt_splice_inner_intron_data_samegene)
#print(alt_splice_inner_intron_data_samegene)
#print(alt_splice_inner_exon_data_samegene)

pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
#################################################################################
ID_Data_alt3 = ID_Data[ID_Data["EventAnnotation"]=="alt-3'"]
ID_Data_alt3 = pd.merge(ID_Data_alt3,alt_splice_change_data,how='inner',on="Alt_Splice")
#################################################################################

AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="alt-3'"]
anno_data = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","CancerSampleRatio","dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data["Diff_Gtex"] = anno_data["Cancer_Mean"] - anno_data["Gtex_Mean"]
anno_data["Diff_TCGA"] = anno_data["Cancer_Mean"] - anno_data["TCGA_Mean"]
anno_data["Diff_Skin"] = anno_data["Cancer_Mean"] - anno_data["Skin_Mean"]

ID_Data_alt3 = pd.merge(ID_Data_alt3,anno_data,how='inner',on="Alt_Splice")
outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/08.Alt-3_anno.txt"
ID_Data_alt3.to_csv(outfile, sep="\t", index=False, header=True)

print(alt_splice_change_data)
print(ID_Data_alt3)


