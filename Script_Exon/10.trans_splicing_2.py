#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   10.trans_splicing_2.py
@Time    :   2025/07/14 16:34:55
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
analysis_dir = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon"
pos3_intersect_file =  f"{analysis_dir}/10.Alt-T-3pos-gene.txt"
pos5_intersect_file =  f"{analysis_dir}/10.Alt-T-5pos-gene.txt"

pos3_intersect_data = pd.read_csv(pos3_intersect_file, sep="\t", header=None, skiprows=0)
pos5_intersect_data = pd.read_csv(pos5_intersect_file, sep="\t", header=None, skiprows=0)

#pos3_intersect_data["gene_Examined"] = pos3_intersect_data[3].str.split(":",expand=True)[0]
pos3_intersect_data = pos3_intersect_data.groupby([3])[8].agg(lambda x: '|'.join(x)).reset_index()
pos5_intersect_data = pos5_intersect_data.groupby([3])[8].agg(lambda x: '|'.join(x)).reset_index()
intersect_data_merge = pd.merge(pos3_intersect_data,pos5_intersect_data,on=3,how='left')
intersect_data_merge.columns = ["Alt_Splice","pos3_gene","pos5_gene"]
intersect_data_merge["Report"] = "-"
for i in intersect_data_merge.index:
    Alt_Splice = intersect_data_merge.loc[i,"Alt_Splice"]
    pos3_gene = re.split("\|",str(intersect_data_merge.loc[i,"pos3_gene"]))
    pos5_gene = re.split("\|",str(intersect_data_merge.loc[i,"pos5_gene"]))
    common_elements = set(pos3_gene) & set(pos5_gene)
    if len(common_elements) <= 0 & Alt_Splice.find("-ENSG")>=0:
        intersect_data_merge.loc[i,"Report"] = "YES"
    else:
        intersect_data_merge.loc[i,"Report"] = "NO"
print(pos3_intersect_data)
print(intersect_data_merge)

#################################################################################
#################################################################################
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
#################################################################################
ID_Data_trans = ID_Data[ID_Data["EventAnnotation"]=="trans-splicing"]
ID_Data_trans = pd.merge(ID_Data_trans,intersect_data_merge,how='inner',on="Alt_Splice")
#################################################################################
print(ID_Data_trans)


AnnoFile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_IR/02.AltSplice_Statistic_Anno.txt"
anno_data = pd.read_csv(AnnoFile,sep="\t",header=0)
anno_data = anno_data[anno_data["EventAnnotation"]=="altPromoter"]
anno_data = anno_data[["Alt_Splice","All_adjPvalue","Cancer_Mean","Gtex_Mean","TCGA_Mean","Skin_Mean","CancerSampleRatio","dataset_ratio","Skin_SampleRatio","Gtex_SampleRatio","TCGA_SampleRatio"]]
anno_data["Diff_Gtex"] = anno_data["Cancer_Mean"] - anno_data["Gtex_Mean"]
anno_data["Diff_TCGA"] = anno_data["Cancer_Mean"] - anno_data["TCGA_Mean"]
anno_data["Diff_Skin"] = anno_data["Cancer_Mean"] - anno_data["Skin_Mean"]
ID_Data_trans = pd.merge(ID_Data_trans,anno_data,how='inner',on="Alt_Splice")

outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/10.Alt-T_anno.txt"
ID_Data_trans.to_csv(outfile, sep="\t", index=False, header=True)
