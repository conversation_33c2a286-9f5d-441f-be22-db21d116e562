#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   06.Exon_Pair_Find_3_Optimized.py
@Time    :   2025/07/04 10:53:35
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
import re
import numpy as np
pair_info_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon_Anno_Parallel.txt"
pair_info_data = pd.read_csv(pair_info_File, sep="\t", header=0, skiprows=0)
pair_info_data = pair_info_data[["Alt_Splice", "Raw_ID", "IntronInfo","strand"]]
pair_info_data = pair_info_data[pair_info_data["IntronInfo"].str.contains("JUNC")]

IR_File = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/03.SampleSplice_Exon.txt"
ID_Data = pd.read_csv(IR_File, sep="\t", header=0, skiprows=0)
ID_Data = pd.merge(ID_Data, pair_info_data, how='inner', on='Alt_Splice')

ID_Data = ID_Data.drop_duplicates(subset=["Alt_Splice"], keep="first")
ID_Data["pos_IGV"] = "-"
ID_Data["bam_Dir"] = "-"
for i in ID_Data.index:
    Alt_Splice = ID_Data.loc[i,"Alt_Splice"]
    pos_Examined = ID_Data.loc[i,"pos_Examined"]
    SampleInfo = ID_Data.loc[i,"SampleID"]
    SampleID = re.split("\.",SampleInfo)[0]
    bam_dir = f"http://*************:5002/01.Liver_RNASeq_Cancer/Liver_RNASeq_Cancer/{SampleID}/05.Aln_Pass2/{SampleID}.Aligned.sortedByCoord.out.bam"
    SupportReads_1 = ID_Data.loc[i,"SupportReads"]
    pos_info = re.split(":|-",pos_Examined)
    chr = pos_info[0]
    pos_1 = pos_info[1]
    pos_2 = pos_info[2]
    if int(pos_1) > int(pos_2):
        pos = f"{chr}:{pos_2}-{pos_1}"
    else:
        pos = f"{chr}:{pos_1}-{pos_2}"
    ID_Data.loc[i,"pos_IGV"] = pos
    ID_Data.loc[i,"bam_Dir"] = bam_dir
print(ID_Data)
print(np.unique(ID_Data["EventAnnotation"]))

#ID_Data = ID_Data.sort_values(by=["EventAnnotation","pos_IGV"],ascending=[True,True])

ID_Data_3 = ID_Data[ID_Data["EventAnnotation"]=="alt-3'"].head(200)
ID_Data_5 = ID_Data[ID_Data["EventAnnotation"]=="alt-5'"].head(200)
ID_Data_cassette = ID_Data[ID_Data["EventAnnotation"]=="cassette-exon"].head(200)
ID_Data_altC = ID_Data[ID_Data["EventAnnotation"]=="alt-C-term"].head(200)
ID_Data_altP = ID_Data[ID_Data["EventAnnotation"]=="altPromoter"].head(200)
ID_Data_trans = ID_Data[ID_Data["EventAnnotation"]=="trans-splicing"].head(200)

print(ID_Data_3)
print(ID_Data_5)
print(ID_Data_cassette)
print(ID_Data_altC)
print(ID_Data_altP)
print(ID_Data_trans)

merge_data = pd.concat([ID_Data_3,ID_Data_5,ID_Data_cassette,ID_Data_altC,ID_Data_altP,ID_Data_trans],ignore_index=True)
merge_data = merge_data.sort_values(by=["EventAnnotation","pos_IGV"],ascending=[True,True])
print(merge_data)

outfile = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/AnalysisResult_Exon/06.SampleSplice_Exon_Anno_Merge_Top_Dup.txt"
merge_data.to_csv(outfile, sep="\t", index=False, header=True)
