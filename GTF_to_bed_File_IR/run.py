import pandas as pd
from Bio import SeqIO

gtf_file = "Homo_sapiens.GRCh38.91.gtf"
bed_exon_file = "exon.bed"
bed_intron_file = "intron.bed"
gene_file = "gene.bed"
gtf_df = pd.read_csv(gtf_file, sep='\t', comment='#', header=None,names=['seqname', 'source', 'feature', 'start', 'end', 'score', 'strand', 'frame', 'attribute'],dtype=str)
# GTF使用1-based坐标，BED使用0-based坐标
# GTF的start需要减1转换为BED格式，end保持不变
gtf_df["start"] = gtf_df["start"].astype(int) - 1  # 转换为0-based
gtf_df["end"] = gtf_df["end"].astype(int)  # end保持不变
gtf_df["chr"] = "chr" + gtf_df["seqname"]
#gtf_df = gtf_df[gtf_df["chr"]=="chr1"]
# 提取外显子
exons = gtf_df[gtf_df['feature'] == 'exon'].copy()
exons['gene_id'] = exons['attribute'].str.extract('gene_id "([^"]+)"')
exons['transcript_id'] = exons['attribute'].str.extract('transcript_id "([^"]+)"')
exons["gene_trans"] = exons["gene_id"] + ":" + exons["transcript_id"]

genes = gtf_df[gtf_df['feature'] == 'gene'].copy()
genes['gene_id'] = genes['attribute'].str.extract('gene_id "([^"]+)"')
# 提取内含子（按转录本分组处理）
introns = []
# 直接按转录本分组，避免重复遍历
for gene_trans, trans_exons in exons.groupby('gene_trans'):
    # 按起始位置排序外显子
    trans_exons = trans_exons.sort_values('start').reset_index(drop=True)

    # 只有多个外显子的转录本才有内含子
    if len(trans_exons) > 1:
        # 计算内含子（外显子之间的区域）
        # 注意：由于外显子坐标已经转换为BED格式(0-based)
        # 内含子start = 前一个外显子的end (BED的end已经是exclusive)
        # 内含子end = 下一个外显子的start (BED的start已经是0-based)
        for i in range(len(trans_exons) - 1):
            intron_start = trans_exons.iloc[i]['end']  # BED格式的end已经是exclusive
            intron_end = trans_exons.iloc[i+1]['start']  # BED格式的start已经是0-based

            # 检查内含子是否有效（start < end）
            if intron_start < intron_end:
                intron_strand = trans_exons.iloc[i]['strand']
                intron_chr = trans_exons.iloc[i]['chr']
                introns.append({
                    'gene_trans': gene_trans,
                    'start': intron_start,
                    'end': intron_end,
                    'gene_id': trans_exons.iloc[i]['gene_id'],
                    'strand': intron_strand,
                    'chr': intron_chr
                })
# 创建内含子DataFrame并处理空情况
if introns:
    introns = pd.DataFrame(introns)
    # 排序并选择BED格式所需的列
    # BED格式：chr, start(0-based), end(exclusive), name, score, strand
    introns = introns.sort_values(by=['chr', 'start'])
    introns = introns[['chr', 'start', 'end', 'strand', 'gene_trans']]
else:
    # 如果没有内含子，创建空的DataFrame
    introns = pd.DataFrame(columns=['chr', 'start', 'end', 'strand', 'gene_trans'])

# 处理外显子输出格式
exons = exons.sort_values(by=['chr', 'start'])
exons = exons[['chr', 'start', 'end', 'gene_trans','strand']]

introns = introns[['chr', 'start', 'end', 'gene_trans','strand']]

genes = genes[['chr', 'start', 'end', 'gene_id','strand']]
print(exons)
print(introns)

# 保存外显子BED
exons.to_csv(bed_exon_file, sep='\t', na_rep='nan', header=False, index=False)
# 保存内含子BED
introns.to_csv(bed_intron_file, sep='\t', na_rep='nan', header=False, index=False)

genes.to_csv(gene_file, sep='\t', na_rep='nan', header=False, index=False)