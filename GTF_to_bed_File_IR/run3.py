#! /ml/dingyu/00.Software/miniconda3/bin/python3
# -*- encoding: utf-8 -*-
'''
@File    :   run3.py
@Time    :   2025/07/11 10:22:46
<AUTHOR>   DingYu
@Version :   1.0
@Contact :   <EMAIL>
'''
import pandas as pd
exon_info = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon.bed"
exon_trans_last_exon = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon_transcript_last-exon.bed"
exon_gene_first_exon = "/ml/dingyu/01.Shared_Neoantigen/01.SNAF/09.Liver_Data_Analysis/GTF_to_bed_File_IR/exon_gene_first-exon.bed"

exon_data = pd.read_csv(exon_info,sep="\t",header=None)
exon_data[["gene","trans"]] = exon_data[3].str.split(":",expand=True)

exon_data_plus = exon_data[exon_data[4]=="+"]
exon_data_minus = exon_data[exon_data[4]=="-"]

##########################################获取每个基因各个转录本的最后一个Exon
exon_data_trans_plus = exon_data_plus.sort_values(by=["trans",0,1,2],ascending=[True,True,True,True])
#print(exon_data_trans_plus.head(20))
exon_data_trans_minus = exon_data_minus.sort_values(by=["trans",0,2,1],ascending=[True,True,True,True])
#print(exon_data_trans_minus.head(20))
exon_data_trans_plus = exon_data_trans_plus.drop_duplicates(subset=["trans"],keep='last')
exon_data_trans_minus = exon_data_trans_minus.drop_duplicates(subset=["trans"],keep='first')
exon_data_trans_lastExon = pd.concat([exon_data_trans_plus,exon_data_trans_minus],ignore_index=True)
#print(exon_data_trans_plus.head(20))
#print(exon_data_trans_minus.head(20))
##########################################获取每个基因的第一个Exon
exon_data_gene_plus = exon_data_plus.sort_values(by=["gene",0,1,2],ascending=[True,True,True,True])
exon_data_gene_minus = exon_data_minus.sort_values(by=["gene",0,2,1],ascending=[True,True,True,True])
exon_data_gene_plus = exon_data_gene_plus.drop_duplicates(subset=["gene"],keep='first')
exon_data_gene_minus = exon_data_gene_minus.drop_duplicates(subset=["gene"],keep='last')
exon_data_gene_firstExon = pd.concat([exon_data_gene_plus,exon_data_gene_minus],ignore_index=True)
print(exon_data_gene_plus)
print(exon_data_gene_minus)

exon_data_trans_lastExon[[0,1,2,3,4]].to_csv(exon_trans_last_exon,sep="\t",header=False,index=False)
exon_data_gene_firstExon[[0,1,2,3,4]].to_csv(exon_gene_first_exon,sep="\t",header=False,index=False)

